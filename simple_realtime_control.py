#!/usr/bin/env python3

import rospy
from geometry_msgs.msg import Twist
import sys
import tty
import termios
import select

class SimpleRealtimeControl:
    def __init__(self):
        rospy.init_node('simple_realtime_control')
        self.pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
        
        # 速度设置
        self.linear_speed = 0.3
        self.angular_speed = 0.6
        
        # 保存终端设置
        self.old_settings = termios.tcgetattr(sys.stdin)
        
        print("=== 简单实时遥控 ===")
        print("实时按键控制:")
        print("W - 前进")
        print("S - 后退")
        print("A - 左转") 
        print("D - 右转")
        print("空格 - 停止")
        print("Q - 退出")
        print("==================")

    def get_key(self):
        """获取按键，非阻塞"""
        tty.setraw(sys.stdin.fileno())
        rlist, _, _ = select.select([sys.stdin], [], [], 0.1)
        if rlist:
            key = sys.stdin.read(1)
        else:
            key = ''
        termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)
        return key

    def send_cmd(self, linear_x=0.0, angular_z=0.0):
        """发送速度命令"""
        twist = Twist()
        twist.linear.x = linear_x
        twist.angular.z = angular_z
        self.pub.publish(twist)

    def run(self):
        """主运行循环"""
        print("开始实时控制，按键立即响应...")
        
        try:
            while not rospy.is_shutdown():
                key = self.get_key().lower()
                
                if key == 'w':
                    self.send_cmd(self.linear_speed, 0.0)
                    print("\r前进", end='', flush=True)
                elif key == 's':
                    self.send_cmd(-self.linear_speed, 0.0)
                    print("\r后退", end='', flush=True)
                elif key == 'a':
                    self.send_cmd(0.0, self.angular_speed)
                    print("\r左转", end='', flush=True)
                elif key == 'd':
                    self.send_cmd(0.0, -self.angular_speed)
                    print("\r右转", end='', flush=True)
                elif key == ' ':
                    self.send_cmd(0.0, 0.0)
                    print("\r停止", end='', flush=True)
                elif key == 'q' or ord(key) == 3:  # q 或 Ctrl+C
                    print("\n退出控制")
                    break
                elif key == '':
                    # 没有按键时停止
                    self.send_cmd(0.0, 0.0)
                    print("\r待命", end='', flush=True)
                    
        except KeyboardInterrupt:
            print("\n收到中断信号")
        finally:
            # 确保机器人停止
            self.send_cmd(0.0, 0.0)
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)
            print("控制结束")

if __name__ == '__main__':
    try:
        controller = SimpleRealtimeControl()
        controller.run()
    except rospy.ROSInterruptException:
        pass
