#!/bin/bash

echo "🎯 测试当前车辆位置和导航系统"
echo "================================"

# 设置ROS环境
source /home/<USER>/ucar_ws/devel/setup.bash
export ROS_MASTER_URI=http://**************:11311
export ROS_IP=**************
export ROS_HOSTNAME=**************

echo "1. 检查当前TF位置..."
timeout 3 rosrun tf tf_echo map base_link | head -5

echo
echo "2. 检查AMCL位置..."
timeout 3 rostopic echo /amcl_pose -n 1

echo
echo "3. 设置正确的初始位置..."
./set_initial_pose.sh

echo
echo "4. 再次检查位置..."
sleep 2
timeout 3 rosrun tf tf_echo map base_link | head -5

echo
echo "✅ 位置检查完成"
