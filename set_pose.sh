#!/bin/bash

# 设置机器人位置的通用脚本
# 用法: ./set_pose.sh [x] [y] [yaw_degrees]
# 如果不提供参数，使用默认的初始位置

# 设置ROS环境
source /home/<USER>/ucar_ws/devel/setup.bash
export ROS_MASTER_URI=http://localhost:11311
export ROS_IP=**************
export ROS_HOSTNAME=**************

# 默认初始位置
DEFAULT_X=0.025
DEFAULT_Y=0.075
DEFAULT_YAW_DEG=-0.403

# 获取参数或使用默认值
X=${1:-$DEFAULT_X}
Y=${2:-$DEFAULT_Y}
YAW_DEG=${3:-$DEFAULT_YAW_DEG}

# 将角度转换为四元数 (简化版，只考虑Z轴旋转)
YAW_RAD=$(echo "scale=6; $YAW_DEG * 3.14159265359 / 180" | bc -l)
Z_QUAT=$(echo "scale=6; s($YAW_RAD / 2)" | bc -l)
W_QUAT=$(echo "scale=6; c($YAW_RAD / 2)" | bc -l)

echo "设置机器人位置..."
echo "位置: ($X, $Y, 0.000)"
echo "朝向: ${YAW_DEG}度"
echo "四元数: z=$Z_QUAT, w=$W_QUAT"

# 发布位置
rostopic pub -1 /initialpose geometry_msgs/PoseWithCovarianceStamped "header:
  seq: 0
  stamp:
    secs: 0
    nsecs: 0
  frame_id: 'map'
pose:
  pose:
    position:
      x: $X
      y: $Y
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: $Z_QUAT
      w: $W_QUAT
  covariance: [0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.06853891945200942]"

echo "✅ 位置设置完成！"
echo ""
echo "用法示例:"
echo "  ./set_pose.sh                    # 使用默认初始位置"
echo "  ./set_pose.sh 1.0 2.0 90        # 设置到位置(1,2)，朝向90度"
echo "  ./set_pose.sh 0 0 0              # 设置到原点，朝向0度"
