#!/usr/bin/env python3
"""
采集车辆转90/180/270度的角度数据
"""

import subprocess
import time
import math
import json
from datetime import datetime

def run_tf_command():
    """运行TF命令获取当前位置和角度"""
    cmd = [
        'bash', '-c',
        'source /home/<USER>/ucar_ws/devel/setup.bash && '
        'export ROS_MASTER_URI=http://localhost:11311 && '
        'export ROS_IP=************** && '
        'export ROS_HOSTNAME=************** && '
        'timeout 10 rosrun tf tf_echo map base_link'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        return result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return None, "命令超时"
    except Exception as e:
        return None, str(e)

def parse_tf_output(output):
    """解析TF输出，提取位置和角度信息"""
    if not output:
        return None
    
    lines = output.strip().split('\n')
    position = None
    orientation = None
    
    for i, line in enumerate(lines):
        if 'Translation:' in line:
            # 查找包含x, y, z坐标的行
            for j in range(i, min(i+5, len(lines))):
                if 'x:' in lines[j] and 'y:' in lines[j] and 'z:' in lines[j]:
                    # 解析坐标
                    coords = lines[j].strip()
                    try:
                        x = float(coords.split('x:')[1].split(',')[0].strip())
                        y = float(coords.split('y:')[1].split(',')[0].strip())
                        z = float(coords.split('z:')[1].strip())
                        position = (x, y, z)
                    except:
                        pass
                    break
        
        elif 'Rotation:' in line:
            # 查找四元数
            for j in range(i, min(i+5, len(lines))):
                if 'x:' in lines[j] and 'y:' in lines[j] and 'z:' in lines[j] and 'w:' in lines[j]:
                    coords = lines[j].strip()
                    try:
                        qx = float(coords.split('x:')[1].split(',')[0].strip())
                        qy = float(coords.split('y:')[1].split(',')[0].strip())
                        qz = float(coords.split('z:')[1].split(',')[0].strip())
                        qw = float(coords.split('w:')[1].strip())
                        orientation = (qx, qy, qz, qw)
                    except:
                        pass
                    break
    
    return position, orientation

def quaternion_to_euler(qx, qy, qz, qw):
    """将四元数转换为欧拉角（度）"""
    # 计算yaw角（绕z轴旋转）
    siny_cosp = 2 * (qw * qz + qx * qy)
    cosy_cosp = 1 - 2 * (qy * qy + qz * qz)
    yaw = math.atan2(siny_cosp, cosy_cosp)
    
    # 转换为度
    yaw_deg = math.degrees(yaw)
    
    return yaw_deg

def collect_angle_data():
    """采集角度数据"""
    print("🎯 车辆角度数据采集工具")
    print("=" * 50)
    print()
    
    angles_to_collect = [
        ("初始位置", "0°"),
        ("右转90度", "90°"),
        ("掉头180度", "180°"),
        ("左转270度", "270°")
    ]
    
    collected_data = {}
    
    for description, target_angle in angles_to_collect:
        print(f"📍 请将车辆调整到: {description} ({target_angle})")
        input("   按回车键开始采集数据...")
        
        print("🔄 正在采集TF数据...")
        
        # 采集多次数据取平均值
        positions = []
        orientations = []
        
        for i in range(3):
            stdout, stderr = run_tf_command()
            
            if stdout:
                pos, orient = parse_tf_output(stdout)
                if pos and orient:
                    positions.append(pos)
                    orientations.append(orient)
                    print(f"   采集 {i+1}/3: 位置({pos[0]:.3f}, {pos[1]:.3f}) 角度{quaternion_to_euler(*orient):.1f}°")
                else:
                    print(f"   采集 {i+1}/3: 解析失败")
            else:
                print(f"   采集 {i+1}/3: 命令失败 - {stderr}")
            
            if i < 2:  # 不是最后一次
                time.sleep(1)
        
        if positions and orientations:
            # 计算平均值
            avg_pos = (
                sum(p[0] for p in positions) / len(positions),
                sum(p[1] for p in positions) / len(positions),
                sum(p[2] for p in positions) / len(positions)
            )
            avg_orient = (
                sum(o[0] for o in orientations) / len(orientations),
                sum(o[1] for o in orientations) / len(orientations),
                sum(o[2] for o in orientations) / len(orientations),
                sum(o[3] for o in orientations) / len(orientations)
            )
            
            avg_yaw = quaternion_to_euler(*avg_orient)
            
            collected_data[target_angle] = {
                'description': description,
                'position': avg_pos,
                'orientation': avg_orient,
                'yaw_degrees': avg_yaw,
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"✅ {description} 数据采集完成:")
            print(f"   位置: ({avg_pos[0]:.3f}, {avg_pos[1]:.3f}, {avg_pos[2]:.3f})")
            print(f"   角度: {avg_yaw:.1f}°")
            print(f"   四元数: ({avg_orient[0]:.3f}, {avg_orient[1]:.3f}, {avg_orient[2]:.3f}, {avg_orient[3]:.3f})")
        else:
            print(f"❌ {description} 数据采集失败")
        
        print()
    
    return collected_data

def save_data(data):
    """保存采集的数据"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存JSON格式
    json_filename = f"rotation_angles_{timestamp}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    # 保存可读格式
    txt_filename = f"rotation_angles_{timestamp}.txt"
    with open(txt_filename, 'w', encoding='utf-8') as f:
        f.write("车辆角度数据采集结果\n")
        f.write("=" * 50 + "\n")
        f.write(f"采集时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for angle, info in data.items():
            f.write(f"{info['description']} ({angle}):\n")
            f.write(f"  位置: ({info['position'][0]:.3f}, {info['position'][1]:.3f}, {info['position'][2]:.3f})\n")
            f.write(f"  角度: {info['yaw_degrees']:.1f}°\n")
            f.write(f"  四元数: ({info['orientation'][0]:.3f}, {info['orientation'][1]:.3f}, {info['orientation'][2]:.3f}, {info['orientation'][3]:.3f})\n")
            f.write("\n")
    
    print(f"📁 数据已保存到:")
    print(f"   {json_filename}")
    print(f"   {txt_filename}")

def main():
    """主函数"""
    try:
        data = collect_angle_data()
        
        if data:
            save_data(data)
            
            print("📊 采集结果摘要:")
            print("-" * 30)
            for angle, info in data.items():
                print(f"{info['description']:12} ({angle:>4}): 位置({info['position'][0]:6.3f}, {info['position'][1]:6.3f}) 角度{info['yaw_degrees']:6.1f}°")
        else:
            print("❌ 没有采集到有效数据")
            
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断采集")
    except Exception as e:
        print(f"\n❌ 采集过程中出错: {e}")

if __name__ == '__main__':
    main()
