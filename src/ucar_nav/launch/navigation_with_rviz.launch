<?xml version="1.0"?>
<launch>
  <!-- 启动完整的导航系统 -->
  <include file="$(find ucar_nav)/launch/ucar_navigation.launch" />

  <!-- 等待系统启动 -->
  <node pkg="rostopic" type="rostopic" name="wait_for_map"
        args="echo /map -n 1" output="screen" />
  
  <node pkg="rostopic" type="rostopic" name="wait_for_amcl"
        args="echo /amcl_pose -n 1" output="screen" />

  <!-- 延迟启动初始位置设置 -->
  <node pkg="rostopic" type="rostopic" name="set_initial_pose"
        args="pub -l /initialpose geometry_msgs/PoseWithCovarianceStamped '{header: {frame_id: map}, pose: {pose: {position: {x: 0.065, y: 0.071, z: 0.0}, orientation: {x: 0.0, y: 0.0, z: -0.002, w: 1.000}}, covariance: [0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.06853891945200942]}}'"
        launch-prefix="bash -c 'sleep 5; $0 $@' "
        output="screen" />

  <!-- 启动RViz并加载自动配置 -->
  <node pkg="rviz" type="rviz" name="rviz" 
        args="-d $(find ucar_nav)/rviz/navigation_auto.rviz" 
        launch-prefix="bash -c 'sleep 3; $0 $@' "
        output="screen" />

</launch>
