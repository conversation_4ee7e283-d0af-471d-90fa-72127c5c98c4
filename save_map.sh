#!/bin/bash
# UCAR 地图保存脚本

echo "=== UCAR 地图保存脚本 ==="
echo ""

# 设置工作空间环境
cd /home/<USER>/ucar_ws
source devel/setup.bash

# 检查map话题是否存在
echo "检查地图话题是否可用..."
if ! rostopic list | grep -q "/map"; then
    echo "错误: 未找到 /map 话题"
    echo "请确保建图系统正在运行"
    exit 1
fi

# 获取当前时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 设置默认地图名称
DEFAULT_MAP_NAME="ucar_map_${TIMESTAMP}"

# 询问用户地图名称
echo "请输入地图名称 (默认: ${DEFAULT_MAP_NAME}):"
read -r MAP_NAME

# 如果用户没有输入，使用默认名称
if [ -z "$MAP_NAME" ]; then
    MAP_NAME=$DEFAULT_MAP_NAME
fi

# 设置地图保存路径
MAP_PATH="/home/<USER>/ucar_ws/map/${MAP_NAME}"

echo ""
echo "正在保存地图到: ${MAP_PATH}"
echo "请稍等..."

# 保存地图
if rosrun map_server map_saver -f "$MAP_PATH"; then
    echo ""
    echo "=== 地图保存成功! ==="
    echo "地图文件:"
    echo "  - ${MAP_PATH}.pgm (图像文件)"
    echo "  - ${MAP_PATH}.yaml (配置文件)"
    echo ""
    
    # 显示地图信息
    if [ -f "${MAP_PATH}.yaml" ]; then
        echo "地图配置信息:"
        cat "${MAP_PATH}.yaml"
        echo ""
    fi
    
    echo "您现在可以在导航中使用这个地图:"
    echo "1. 编辑导航launch文件中的地图路径"
    echo "2. 或者将地图文件复制到默认位置"
    echo ""
    echo "建议的下一步:"
    echo "1. 停止建图系统 (Ctrl+C)"
    echo "2. 启动导航系统测试新地图"
    echo "3. 使用以下命令启动导航:"
    echo "   roslaunch ucar_nav ucar_navigation.launch"
    
else
    echo ""
    echo "错误: 地图保存失败"
    echo "请检查:"
    echo "1. 建图系统是否正在运行"
    echo "2. /map 话题是否有数据"
    echo "3. 磁盘空间是否充足"
    echo "4. 目录权限是否正确"
fi

echo ""
