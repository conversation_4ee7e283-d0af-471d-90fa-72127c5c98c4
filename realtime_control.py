#!/usr/bin/env python3

import rospy
from geometry_msgs.msg import Twist
import sys
import tty
import termios
import select
import threading
import time

class RealtimeRobotControl:
    def __init__(self):
        rospy.init_node('realtime_robot_control', anonymous=True)
        self.pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
        
        # 速度设置
        self.linear_speed = 0.3
        self.angular_speed = 0.6
        
        # 当前按键状态
        self.keys_pressed = set()
        
        # 控制标志
        self.running = True
        
        # 保存终端设置
        self.old_settings = termios.tcgetattr(sys.stdin)
        
        print("=== 实时机器人遥控 ===")
        print("按住按键控制机器人:")
        print("W - 前进")
        print("S - 后退") 
        print("A - 左转")
        print("D - 右转")
        print("松开按键自动停止")
        print("ESC 或 Ctrl+C - 退出")
        print("========================")
        print("开始实时控制...")

    def setup_terminal(self):
        """设置终端为非阻塞模式"""
        tty.setraw(sys.stdin.fileno())

    def restore_terminal(self):
        """恢复终端设置"""
        termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)

    def get_pressed_keys(self):
        """获取当前按下的按键"""
        keys = set()
        
        # 检查是否有输入
        if select.select([sys.stdin], [], [], 0) == ([sys.stdin], [], []):
            key = sys.stdin.read(1)
            
            # 处理特殊键
            if ord(key) == 27:  # ESC键
                return {'ESC'}
            elif ord(key) == 3:  # Ctrl+C
                return {'CTRL_C'}
            else:
                keys.add(key.lower())
        
        return keys

    def calculate_velocity(self):
        """根据按键计算速度"""
        linear_x = 0.0
        angular_z = 0.0
        
        if 'w' in self.keys_pressed:
            linear_x += self.linear_speed
        if 's' in self.keys_pressed:
            linear_x -= self.linear_speed
        if 'a' in self.keys_pressed:
            angular_z += self.angular_speed
        if 'd' in self.keys_pressed:
            angular_z -= self.angular_speed
            
        return linear_x, angular_z

    def send_velocity(self, linear_x, angular_z):
        """发送速度命令"""
        twist = Twist()
        twist.linear.x = linear_x
        twist.angular.z = angular_z
        self.pub.publish(twist)

    def control_loop(self):
        """主控制循环"""
        last_keys = set()
        
        while self.running and not rospy.is_shutdown():
            try:
                # 获取当前按键
                current_keys = self.get_pressed_keys()
                
                # 检查退出条件
                if 'ESC' in current_keys or 'CTRL_C' in current_keys:
                    break
                
                # 更新按键状态
                self.keys_pressed.update(current_keys)
                
                # 移除长时间未按的键（模拟松开）
                # 这里使用简单的超时机制
                keys_to_remove = set()
                for key in self.keys_pressed:
                    if key not in current_keys and key in last_keys:
                        keys_to_remove.add(key)
                
                # 如果没有新按键，清空所有按键（模拟全部松开）
                if not current_keys:
                    self.keys_pressed.clear()
                
                # 计算并发送速度
                linear_x, angular_z = self.calculate_velocity()
                self.send_velocity(linear_x, angular_z)
                
                # 显示当前状态
                if self.keys_pressed:
                    status = f"\r按键: {sorted(self.keys_pressed)} | 线速度: {linear_x:.1f} | 角速度: {angular_z:.1f}"
                    print(status, end='', flush=True)
                else:
                    print("\r等待按键...", end='', flush=True)
                
                last_keys = current_keys.copy()
                time.sleep(0.05)  # 20Hz更新频率
                
            except Exception as e:
                print(f"\n控制循环错误: {e}")
                break

    def run(self):
        """运行控制器"""
        try:
            self.setup_terminal()
            self.control_loop()
        except KeyboardInterrupt:
            print("\n收到中断信号")
        except Exception as e:
            print(f"\n运行错误: {e}")
        finally:
            # 停止机器人
            self.send_velocity(0.0, 0.0)
            self.restore_terminal()
            print("\n实时控制结束")

if __name__ == '__main__':
    try:
        controller = RealtimeRobotControl()
        controller.run()
    except rospy.ROSInterruptException:
        pass
