#!/bin/bash
# 连续控制的机器人控制脚本

echo "=== 连续机器人控制 ==="
echo "实时响应控制方案"
echo ""

# 设置ROS环境
cd /home/<USER>/ucar_ws
source devel/setup.bash
export ROS_MASTER_URI=http://localhost:11311
export ROS_IP=**************
export ROS_HOSTNAME=**************

echo "控制说明："
echo "1 - 前进 (持续)"
echo "2 - 后退 (持续)"
echo "3 - 左转 (持续)"
echo "4 - 右转 (持续)"
echo "5 - 左前 (持续)"
echo "6 - 右前 (持续)"
echo "0 - 停止"
echo "q - 退出"
echo ""
echo "提示：按数字键后机器人会持续移动，按0停止"
echo ""

# 定义控制函数
start_forward() {
    rostopic pub /cmd_vel geometry_msgs/Twist '{linear: {x: 0.3, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' -r 10 &
    CONTROL_PID=$!
}

start_backward() {
    rostopic pub /cmd_vel geometry_msgs/Twist '{linear: {x: -0.3, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' -r 10 &
    CONTROL_PID=$!
}

start_left() {
    rostopic pub /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.6}}' -r 10 &
    CONTROL_PID=$!
}

start_right() {
    rostopic pub /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: -0.6}}' -r 10 &
    CONTROL_PID=$!
}

start_left_forward() {
    rostopic pub /cmd_vel geometry_msgs/Twist '{linear: {x: 0.3, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.4}}' -r 10 &
    CONTROL_PID=$!
}

start_right_forward() {
    rostopic pub /cmd_vel geometry_msgs/Twist '{linear: {x: 0.3, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: -0.4}}' -r 10 &
    CONTROL_PID=$!
}

stop_robot() {
    if [ ! -z "$CONTROL_PID" ]; then
        kill $CONTROL_PID 2>/dev/null
        CONTROL_PID=""
    fi
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' > /dev/null 2>&1
}

# 清理函数
cleanup() {
    echo ""
    echo "正在停止机器人..."
    stop_robot
    echo "控制结束"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

CONTROL_PID=""

# 主控制循环
while true; do
    echo -n "输入命令: "
    read -n 1 key
    echo ""
    
    case $key in
        1)
            echo "开始前进..."
            stop_robot
            start_forward
            ;;
        2)
            echo "开始后退..."
            stop_robot
            start_backward
            ;;
        3)
            echo "开始左转..."
            stop_robot
            start_left
            ;;
        4)
            echo "开始右转..."
            stop_robot
            start_right
            ;;
        5)
            echo "开始左前移动..."
            stop_robot
            start_left_forward
            ;;
        6)
            echo "开始右前移动..."
            stop_robot
            start_right_forward
            ;;
        0)
            echo "停止机器人..."
            stop_robot
            ;;
        q)
            echo "退出控制..."
            stop_robot
            break
            ;;
        *)
            echo "无效命令，请使用 1/2/3/4/5/6/0/q"
            ;;
    esac
done

cleanup
