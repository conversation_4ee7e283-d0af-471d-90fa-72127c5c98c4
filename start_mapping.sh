#!/bin/bash
# UCAR 建图启动脚本
# 使用gmapping进行SLAM建图

echo "=== UCAR 建图系统启动脚本 ==="
echo "请确保机器人已连接并且激光雷达正常工作"
echo ""

# 检查ROS环境
if ! command -v roscore &> /dev/null; then
    echo "错误: ROS环境未正确设置"
    exit 1
fi

# 设置工作空间环境
cd /home/<USER>/ucar_ws
source devel/setup.bash

echo "1. 启动ROS核心..."
if ! pgrep -x "rosmaster" > /dev/null; then
    roscore &
    sleep 3
    echo "   ROS核心已启动"
else
    echo "   ROS核心已在运行"
fi

echo ""
echo "2. 启动建图系统..."
echo "   - 底盘控制器"
echo "   - 激光雷达"
echo "   - gmapping SLAM"

# 启动建图launch文件
roslaunch ucar_map ucar_mapping.launch &
MAPPING_PID=$!
sleep 5

echo ""
echo "3. 等待系统初始化..."
sleep 3

echo ""
echo "=== 建图系统已启动 ==="
echo ""
echo "下一步操作："
echo "1. 在新终端中运行以下命令启动RViz可视化："
echo "   rosrun rviz rviz"
echo ""
echo "2. 在RViz中添加以下显示项："
echo "   - Map (话题: /map)"
echo "   - LaserScan (话题: /scan)"
echo "   - TF"
echo "   - RobotModel"
echo ""
echo "3. 在新终端中运行以下命令控制机器人移动："
echo "   rosrun teleop_twist_keyboard teleop_twist_keyboard.py"
echo "   或者使用: ./control-car.sh"
echo ""
echo "4. 建图完成后，运行以下命令保存地图："
echo "   rosrun map_server map_saver -f /home/<USER>/ucar_ws/map/my_new_map"
echo ""
echo "建图技巧："
echo "- 缓慢移动机器人，让激光雷达充分扫描"
echo "- 尽量覆盖所有需要导航的区域"
echo "- 在RViz中实时查看地图质量"
echo ""
echo "按 Ctrl+C 停止建图系统"

# 等待用户中断
trap 'echo "正在停止建图系统..."; kill $MAPPING_PID; exit' INT
wait $MAPPING_PID
