#!/bin/bash

# 快速获取当前角度并格式化输出

echo "🎯 当前车辆位置和角度"
echo "===================="

# 设置ROS环境
source /home/<USER>/ucar_ws/devel/setup.bash
export ROS_MASTER_URI=http://localhost:11311
export ROS_IP=**************
export ROS_HOSTNAME=**************

# 获取TF数据
echo "🔄 正在获取数据..."
tf_output=$(timeout 5 rosrun tf tf_echo map base_link 2>/dev/null | head -15)

if [ $? -eq 0 ] && [ ! -z "$tf_output" ]; then
    echo "✅ 数据获取成功"
    echo

    # 提取关键信息
    translation=$(echo "$tf_output" | grep "Translation:" -A 1 | tail -1 | sed 's/^[[:space:]]*//')
    quaternion=$(echo "$tf_output" | grep "Quaternion" | sed 's/.*Quaternion //')
    rpy_degree=$(echo "$tf_output" | grep "RPY (degree)" | sed 's/.*RPY (degree) //')
    
    echo "📍 位置坐标: $translation"
    echo "🧭 角度(度): $rpy_degree"
    echo "🔄 四元数:   $quaternion"
    echo
    
    # 提取数值用于复制
    if [[ $translation =~ \[([^,]+),([^,]+),([^\]]+)\] ]]; then
        x="${BASH_REMATCH[1]}"
        y="${BASH_REMATCH[2]}"
        z="${BASH_REMATCH[3]}"
        echo "📋 复制用格式:"
        echo "   位置: ($x, $y, $z)"
    fi
    
    if [[ $rpy_degree =~ \[([^,]+),([^,]+),([^\]]+)\] ]]; then
        roll="${BASH_REMATCH[1]}"
        pitch="${BASH_REMATCH[2]}"
        yaw="${BASH_REMATCH[3]}"
        echo "   角度: Roll=$roll°, Pitch=$pitch°, Yaw=$yaw°"
    fi
    
    if [[ $quaternion =~ \[([^,]+),([^,]+),([^,]+),([^\]]+)\] ]]; then
        qx="${BASH_REMATCH[1]}"
        qy="${BASH_REMATCH[2]}"
        qz="${BASH_REMATCH[3]}"
        qw="${BASH_REMATCH[4]}"
        echo "   四元数: ($qx, $qy, $qz, $qw)"
    fi
    
else
    echo "❌ 数据获取失败"
    echo "   请检查:"
    echo "   - ROS master是否运行"
    echo "   - TF数据是否发布"
    echo "   - 网络连接是否正常"
fi

echo
