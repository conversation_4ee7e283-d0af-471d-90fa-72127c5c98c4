#!/bin/bash

# 启动导航系统和RViz的便捷脚本
# 自动加载地图数据和设置初始位置

echo "🚀 启动导航系统和RViz..."
echo "📍 将自动设置初始位置: (0.065, 0.071, -0.286°)"
echo "🗺️  将自动加载地图: /home/<USER>/ucar_ws/map/map_0416_image.yaml"
echo ""

# 设置ROS环境
source /home/<USER>/ucar_ws/devel/setup.bash
export ROS_MASTER_URI=http://localhost:11311
export ROS_IP=**************
export ROS_HOSTNAME=**************

# 检查roscore是否运行
if ! pgrep -f "roscore" > /dev/null; then
    echo "⚠️  roscore未运行，正在启动..."
    roscore &
    sleep 3
fi

echo "✅ 启动导航系统和RViz..."
roslaunch ucar_nav navigation_with_rviz.launch

echo ""
echo "🎯 使用说明:"
echo "1. 等待RViz完全加载"
echo "2. 机器人位置会自动设置到初始位置"
echo "3. 使用'2D Nav Goal'工具设置目标点进行导航"
echo "4. 使用'2D Pose Estimate'工具重新设置机器人位置"
echo ""
echo "📋 快捷键:"
echo "- Ctrl+C: 停止所有节点"
echo "- 在RViz中按'r': 重置视图"
