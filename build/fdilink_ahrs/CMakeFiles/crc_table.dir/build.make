# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ucar_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ucar_ws/build

# Include any dependencies generated for this target.
include fdilink_ahrs/CMakeFiles/crc_table.dir/depend.make

# Include the progress variables for this target.
include fdilink_ahrs/CMakeFiles/crc_table.dir/progress.make

# Include the compile flags for this target's objects.
include fdilink_ahrs/CMakeFiles/crc_table.dir/flags.make

fdilink_ahrs/CMakeFiles/crc_table.dir/src/crc_table.cpp.o: fdilink_ahrs/CMakeFiles/crc_table.dir/flags.make
fdilink_ahrs/CMakeFiles/crc_table.dir/src/crc_table.cpp.o: /home/<USER>/ucar_ws/src/fdilink_ahrs/src/crc_table.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object fdilink_ahrs/CMakeFiles/crc_table.dir/src/crc_table.cpp.o"
	cd /home/<USER>/ucar_ws/build/fdilink_ahrs && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/crc_table.dir/src/crc_table.cpp.o -c /home/<USER>/ucar_ws/src/fdilink_ahrs/src/crc_table.cpp

fdilink_ahrs/CMakeFiles/crc_table.dir/src/crc_table.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/crc_table.dir/src/crc_table.cpp.i"
	cd /home/<USER>/ucar_ws/build/fdilink_ahrs && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/ucar_ws/src/fdilink_ahrs/src/crc_table.cpp > CMakeFiles/crc_table.dir/src/crc_table.cpp.i

fdilink_ahrs/CMakeFiles/crc_table.dir/src/crc_table.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/crc_table.dir/src/crc_table.cpp.s"
	cd /home/<USER>/ucar_ws/build/fdilink_ahrs && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/ucar_ws/src/fdilink_ahrs/src/crc_table.cpp -o CMakeFiles/crc_table.dir/src/crc_table.cpp.s

# Object files for target crc_table
crc_table_OBJECTS = \
"CMakeFiles/crc_table.dir/src/crc_table.cpp.o"

# External object files for target crc_table
crc_table_EXTERNAL_OBJECTS =

/home/<USER>/ucar_ws/devel/lib/libcrc_table.so: fdilink_ahrs/CMakeFiles/crc_table.dir/src/crc_table.cpp.o
/home/<USER>/ucar_ws/devel/lib/libcrc_table.so: fdilink_ahrs/CMakeFiles/crc_table.dir/build.make
/home/<USER>/ucar_ws/devel/lib/libcrc_table.so: fdilink_ahrs/CMakeFiles/crc_table.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/ucar_ws/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX shared library /home/<USER>/ucar_ws/devel/lib/libcrc_table.so"
	cd /home/<USER>/ucar_ws/build/fdilink_ahrs && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/crc_table.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
fdilink_ahrs/CMakeFiles/crc_table.dir/build: /home/<USER>/ucar_ws/devel/lib/libcrc_table.so

.PHONY : fdilink_ahrs/CMakeFiles/crc_table.dir/build

fdilink_ahrs/CMakeFiles/crc_table.dir/clean:
	cd /home/<USER>/ucar_ws/build/fdilink_ahrs && $(CMAKE_COMMAND) -P CMakeFiles/crc_table.dir/cmake_clean.cmake
.PHONY : fdilink_ahrs/CMakeFiles/crc_table.dir/clean

fdilink_ahrs/CMakeFiles/crc_table.dir/depend:
	cd /home/<USER>/ucar_ws/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/ucar_ws/src /home/<USER>/ucar_ws/src/fdilink_ahrs /home/<USER>/ucar_ws/build /home/<USER>/ucar_ws/build/fdilink_ahrs /home/<USER>/ucar_ws/build/fdilink_ahrs/CMakeFiles/crc_table.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : fdilink_ahrs/CMakeFiles/crc_table.dir/depend

