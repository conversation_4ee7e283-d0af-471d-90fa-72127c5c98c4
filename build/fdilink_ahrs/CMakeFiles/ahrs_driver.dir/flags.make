# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.13

# compile CXX with /usr/bin/c++
CXX_FLAGS =  -std=c++0x  

CXX_DEFINES = -DROSCONSOLE_BACKEND_LOG4CXX -DROS_BUILD_SHARED_LIBS=1 -DROS_PACKAGE_NAME=\"fdilink_ahrs\"

CXX_INCLUDES = -I/home/<USER>/ucar_ws/src/fdilink_ahrs/include -I/home/<USER>/ucar_ws/src/fdilink_ahrs/src -I/home/<USER>/ucar_ws/devel/include -I/home/<USER>/ucar_ws/src/geometry/tf/include -I/home/<USER>/ucar_ws/src/geometry2/tf2/include -I/home/<USER>/ucar_ws/src/geometry2/tf2_msgs/include -I/opt/ros/noetic/include -I/opt/ros/noetic/share/xmlrpcpp/cmake/../../../include/xmlrpcpp -I/usr/include/eigen3 

