/usr/bin/c++   -std=c++0x  -rdynamic -pthread CMakeFiles/ahrs_driver.dir/src/ahrs_driver.cpp.o  -o /home/<USER>/ucar_ws/devel/lib/fdilink_ahrs/ahrs_driver -Wl,-rpath,/home/<USER>/ucar_ws/devel/lib:/opt/ros/noetic/lib /home/<USER>/ucar_ws/devel/lib/libcrc_table.so /home/<USER>/ucar_ws/devel/lib/libtf.so /opt/ros/noetic/lib/libtf2_ros.so /opt/ros/noetic/lib/libactionlib.so /opt/ros/noetic/lib/libmessage_filters.so /opt/ros/noetic/lib/libroscpp.so -lboost_filesystem /opt/ros/noetic/lib/libxmlrpcpp.so /home/<USER>/ucar_ws/devel/lib/libtf2.so /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so -llog4cxx -lboost_regex /opt/ros/noetic/lib/librostime.so /opt/ros/noetic/lib/libcpp_common.so -lboost_system -lboost_thread -lpthread -lboost_chrono -lboost_date_time -lboost_atomic /usr/lib/aarch64-linux-gnu/libconsole_bridge.so.0.4 /opt/ros/noetic/lib/libserial.so /usr/lib/aarch64-linux-gnu/libconsole_bridge.so.0.4 -pthread 
