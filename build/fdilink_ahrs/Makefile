# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ucar_ws/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ucar_ws/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/ucar_ws/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles /home/<USER>/ucar_ws/build/fdilink_ahrs/CMakeFiles/progress.marks
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 fdilink_ahrs/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ucar_ws/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 fdilink_ahrs/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 fdilink_ahrs/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 fdilink_ahrs/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/ucar_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
fdilink_ahrs/CMakeFiles/crc_table.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 fdilink_ahrs/CMakeFiles/crc_table.dir/rule
.PHONY : fdilink_ahrs/CMakeFiles/crc_table.dir/rule

# Convenience name for target.
crc_table: fdilink_ahrs/CMakeFiles/crc_table.dir/rule

.PHONY : crc_table

# fast build rule for target.
crc_table/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f fdilink_ahrs/CMakeFiles/crc_table.dir/build.make fdilink_ahrs/CMakeFiles/crc_table.dir/build
.PHONY : crc_table/fast

# Convenience name for target.
fdilink_ahrs/CMakeFiles/ahrs_driver.dir/rule:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f CMakeFiles/Makefile2 fdilink_ahrs/CMakeFiles/ahrs_driver.dir/rule
.PHONY : fdilink_ahrs/CMakeFiles/ahrs_driver.dir/rule

# Convenience name for target.
ahrs_driver: fdilink_ahrs/CMakeFiles/ahrs_driver.dir/rule

.PHONY : ahrs_driver

# fast build rule for target.
ahrs_driver/fast:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f fdilink_ahrs/CMakeFiles/ahrs_driver.dir/build.make fdilink_ahrs/CMakeFiles/ahrs_driver.dir/build
.PHONY : ahrs_driver/fast

src/ahrs_driver.o: src/ahrs_driver.cpp.o

.PHONY : src/ahrs_driver.o

# target to build an object file
src/ahrs_driver.cpp.o:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f fdilink_ahrs/CMakeFiles/ahrs_driver.dir/build.make fdilink_ahrs/CMakeFiles/ahrs_driver.dir/src/ahrs_driver.cpp.o
.PHONY : src/ahrs_driver.cpp.o

src/ahrs_driver.i: src/ahrs_driver.cpp.i

.PHONY : src/ahrs_driver.i

# target to preprocess a source file
src/ahrs_driver.cpp.i:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f fdilink_ahrs/CMakeFiles/ahrs_driver.dir/build.make fdilink_ahrs/CMakeFiles/ahrs_driver.dir/src/ahrs_driver.cpp.i
.PHONY : src/ahrs_driver.cpp.i

src/ahrs_driver.s: src/ahrs_driver.cpp.s

.PHONY : src/ahrs_driver.s

# target to generate assembly for a file
src/ahrs_driver.cpp.s:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f fdilink_ahrs/CMakeFiles/ahrs_driver.dir/build.make fdilink_ahrs/CMakeFiles/ahrs_driver.dir/src/ahrs_driver.cpp.s
.PHONY : src/ahrs_driver.cpp.s

src/crc_table.o: src/crc_table.cpp.o

.PHONY : src/crc_table.o

# target to build an object file
src/crc_table.cpp.o:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f fdilink_ahrs/CMakeFiles/crc_table.dir/build.make fdilink_ahrs/CMakeFiles/crc_table.dir/src/crc_table.cpp.o
.PHONY : src/crc_table.cpp.o

src/crc_table.i: src/crc_table.cpp.i

.PHONY : src/crc_table.i

# target to preprocess a source file
src/crc_table.cpp.i:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f fdilink_ahrs/CMakeFiles/crc_table.dir/build.make fdilink_ahrs/CMakeFiles/crc_table.dir/src/crc_table.cpp.i
.PHONY : src/crc_table.cpp.i

src/crc_table.s: src/crc_table.cpp.s

.PHONY : src/crc_table.s

# target to generate assembly for a file
src/crc_table.cpp.s:
	cd /home/<USER>/ucar_ws/build && $(MAKE) -f fdilink_ahrs/CMakeFiles/crc_table.dir/build.make fdilink_ahrs/CMakeFiles/crc_table.dir/src/crc_table.cpp.s
.PHONY : src/crc_table.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... crc_table"
	@echo "... install"
	@echo "... ahrs_driver"
	@echo "... install/local"
	@echo "... test"
	@echo "... list_install_components"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... src/ahrs_driver.o"
	@echo "... src/ahrs_driver.i"
	@echo "... src/ahrs_driver.s"
	@echo "... src/crc_table.o"
	@echo "... src/crc_table.i"
	@echo "... src/crc_table.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/ucar_ws/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

