set(_CATKIN_CURRENT_PACKAGE "fdilink_ahrs")
set(fdilink_ahrs_VERSION "0.0.1")
set(fdilink_ahrs_MAINTAINER "iflytek <<EMAIL>>")
set(fdilink_ahrs_PACKAGE_FORMAT "2")
set(fdilink_ahrs_BUILD_DEPENDS "roscpp" "rospy" "sensor_msgs" "std_msgs" "tf")
set(fdilink_ahrs_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "sensor_msgs" "std_msgs" "tf")
set(fdilink_ahrs_BUILDTOOL_DEPENDS "catkin" "genmsg")
set(fdilink_ahrs_BUILDTOOL_EXPORT_DEPENDS )
set(fdilink_ahrs_EXEC_DEPENDS "roscpp" "rospy" "sensor_msgs" "std_msgs" "tf")
set(fdilink_ahrs_RUN_DEPENDS "roscpp" "rospy" "sensor_msgs" "std_msgs" "tf")
set(fdilink_ahrs_TEST_DEPENDS )
set(fdilink_ahrs_DOC_DEPENDS )
set(fdilink_ahrs_URL_WEBSITE "")
set(fdilink_ahrs_URL_BUGTRACKER "")
set(fdilink_ahrs_URL_REPOSITORY "")
set(fdilink_ahrs_DEPRECATED "")