#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export PATH='/home/<USER>/ucar_ws/devel/bin:/opt/ros/noetic/bin:/home/<USER>/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/remote-cli:/home/<USER>/.local/bin:/home/<USER>/miniconda3/bin:/home/<USER>/miniconda3/condabin:/home/<USER>/.pyenv/shims:/home/<USER>/.pyenv/bin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/home/<USER>/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand'
export PWD='/home/<USER>/ucar_ws/build'