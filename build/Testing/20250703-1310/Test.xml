<?xml version="1.0" encoding="UTF-8"?>
<Site BuildName="(empty)"
	BuildStamp="20250703-1310-Experimental"
	Name="superbrain"
	Generator="ctest-3.13.4"
	CompilerName="/usr/bin/c++"
	CompilerVersion="8.3.0"
	OSName="Linux"
	Hostname="superbrain"
	OSRelease="5.10.176"
	OSVersion="#273 SMP PREEMPT Wed Jun 21 08:02:07 CST 2023"
	OSPlatform="aarch64"
	Is64Bits="1"
	VendorString=""
	VendorID="Unknown Manufacturer"
	FamilyID="8"
	ModelID="0"
	ProcessorCacheSize="0"
	NumberOfLogicalCPU="8"
	NumberOfPhysicalCPU="1"
	TotalVirtualMemory="0"
	TotalPhysicalMemory="7910"
	LogicalProcessorsPerPhysical="8"
	ProcessorClockFrequency="0"
	>
	<Testing>
		<StartDateTime>Jul 03 22:12 CST</StartDateTime>
		<StartTestTime>1751551937</StartTestTime>
		<TestList/>
		<EndDateTime>Jul 03 22:12 CST</EndDateTime>
		<EndTestTime>1751551937</EndTestTime>
		<ElapsedMinutes>0</ElapsedMinutes>
	</Testing>
</Site>
