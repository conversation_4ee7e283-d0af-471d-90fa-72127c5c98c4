<package>
  <name>geometry</name>
  <version>1.12.0</version>
  <description><p>A metapackage for geometry library suite.</p>
    <p><b>Migration</b>: Since ROS Hydro, tf has been "deprecated" in favor of <a href = "http://wiki.ros.org/tf2">tf2</a>. tf2 is an iteration on tf providing generally the same feature set more efficiently. As well as adding a few new features.<br/>
    As tf2 is a major change the tf API has been maintained in its current form. Since tf2 has a superset of the tf features with a subset of the dependencies the tf implementation has been removed and replaced with calls to tf2 under the hood. This will mean that all users will be compatible with tf2. It is recommended for new work to use tf2 directly as it has a cleaner interface. However tf will continue to be supported for through at least J Turtle.
    </p>
  </description>
  <maintainer email="<EMAIL>">Tully Foote</maintainer>
  <license>BSD</license>
  
  <url type="website">http://www.ros.org/wiki/geometry</url>
  <url type="bugtracker">https://code.ros.org/trac/ros-pkg/query?component=geometry&amp;status=assigned&amp;status=new&amp;status=reopened</url>
  <url type="repository">https://kforge.ros.org/geometry/geometry</url>
  
  <author>Tully Foote</author>
  
  <buildtool_depend>catkin</buildtool_depend>

  <!-- Old stack contents -->
  <run_depend>angles</run_depend>
  <run_depend>eigen_conversions</run_depend>
  <run_depend>kdl_conversions</run_depend>
  <run_depend>tf</run_depend>
  <run_depend>tf_conversions</run_depend>

  <export>
    <metapackage/>
  </export>

</package>
