# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/ucar_ws/src/geometry/kdl_conversions/include;/usr/include/eigen3".split(';') if "/home/<USER>/ucar_ws/src/geometry/kdl_conversions/include;/usr/include/eigen3" != "" else []
PROJECT_CATKIN_DEPENDS = "geometry_msgs".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-lkdl_conversions;-lorocos-kdl".split(';') if "-lkdl_conversions;-lorocos-kdl" != "" else []
PROJECT_NAME = "kdl_conversions"
PROJECT_SPACE_DIR = "/home/<USER>/ucar_ws/devel"
PROJECT_VERSION = "1.12.0"
