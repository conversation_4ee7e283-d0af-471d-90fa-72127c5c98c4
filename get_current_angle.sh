#!/bin/bash

# 快速获取当前车辆角度的脚本

echo "🎯 获取当前车辆位置和角度..."
echo "=================================="

# 设置ROS环境
source /home/<USER>/ucar_ws/devel/setup.bash
export ROS_MASTER_URI=http://localhost:11311
export ROS_IP=**************
export ROS_HOSTNAME=**************

echo "🔄 正在获取TF数据..."
echo

# 运行TF命令
timeout 10 rosrun tf tf_echo map base_link

echo
echo "✅ 数据获取完成"
echo
echo "💡 使用说明:"
echo "   - Translation 是位置坐标 (x, y, z)"
echo "   - Rotation 是四元数朝向 (x, y, z, w)"
echo "   - 记录下这些数值用于角度采集"
