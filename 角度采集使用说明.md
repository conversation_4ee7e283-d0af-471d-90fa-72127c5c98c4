# 车辆角度数据采集工具使用说明

## 📋 工具概览

为了采集车辆在不同角度（90°/180°/270°）的TF数据，我创建了以下工具：

### 🛠️ 可用脚本

1. **`simple_angle_check.sh`** - 最简单的角度检查工具 ⭐推荐
2. **`get_current_angle.sh`** - 详细的TF数据获取工具
3. **`collect_angles_interactive.sh`** - 交互式批量采集工具
4. **`collect_rotation_angles.py`** - Python版本的高级采集工具
5. **`angle_collection_template.txt`** - 手动记录模板

## 🎯 推荐使用方法

### 方法一：使用简单检查脚本（推荐）

```bash
# 1. 将车辆调整到目标角度（例如右转90度）
# 2. 运行检查脚本
./simple_angle_check.sh

# 3. 记录输出的数据
```

### 方法二：使用原始命令

```bash
# 直接运行TF命令
source /home/<USER>/ucar_ws/devel/setup.bash && \
export ROS_MASTER_URI=http://localhost:11311 && \
export ROS_IP=************** && \
export ROS_HOSTNAME=************** && \
timeout 10 rosrun tf tf_echo map base_link
```

## 📊 数据采集步骤

### 第1步：准备工作
- 确保ROS系统正常运行
- 确保导航系统已启动
- 确保车辆在已知的初始位置

### 第2步：采集0度（初始位置）
```bash
./simple_angle_check.sh
```
记录输出数据：
- Translation: [x, y, z]
- Quaternion: [x, y, z, w]  
- RPY (degree): [roll, pitch, yaw]

### 第3步：采集90度（右转）
1. 手动或遥控将车辆右转90度
2. 确保车辆完全静止
3. 运行：`./simple_angle_check.sh`
4. 记录数据

### 第4步：采集180度（掉头）
1. 将车辆调整到180度位置
2. 确保车辆完全静止
3. 运行：`./simple_angle_check.sh`
4. 记录数据

### 第5步：采集270度（左转）
1. 将车辆调整到270度位置
2. 确保车辆完全静止
3. 运行：`./simple_angle_check.sh`
4. 记录数据

## 📝 数据记录格式

### 当前车辆位置（示例）
```
At time 1751729652.015
- Translation: [0.050, 0.092, 0.000]
- Rotation: in Quaternion [0.000, 0.000, -0.011, 1.000]
            in RPY (radian) [0.000, -0.000, -0.022]
            in RPY (degree) [0.000, -0.000, -1.283]
```

### 需要记录的关键数据
- **位置坐标**: Translation [x, y, z]
- **四元数朝向**: Quaternion [x, y, z, w]
- **欧拉角**: RPY (degree) [roll, pitch, yaw]

## 🎯 数据分析

### 角度理解
- **Yaw角度**：车辆的朝向角度（最重要）
  - 0° = 正前方
  - 90° = 右转90度
  - 180° = 掉头
  - 270° = 左转90度（或右转-90度）

### 四元数转换
四元数 [x, y, z, w] 可以转换为欧拉角：
```
yaw = atan2(2*(w*z + x*y), 1-2*(y*y + z*z))
```

## 🔧 故障排除

### 如果命令失败
1. 检查ROS master是否运行：`roscore`
2. 检查TF数据是否发布：`rostopic list | grep tf`
3. 检查网络配置是否正确

### 如果数据不稳定
1. 确保车辆完全静止
2. 多次采集取平均值
3. 检查定位系统是否正常

## 📁 输出文件

使用交互式工具会生成：
- `angle_data_YYYYMMDD_HHMMSS.txt` - 采集结果文件
- `rotation_angles_YYYYMMDD_HHMMSS.json` - JSON格式数据

## 💡 使用建议

1. **采集顺序**：建议按 0° → 90° → 180° → 270° 的顺序采集
2. **多次验证**：每个角度采集2-3次确保数据准确性
3. **环境一致**：在相同的环境和位置进行所有采集
4. **记录详细**：记录采集时间、环境条件等信息

## 🎉 完成后

采集完成后，您将获得车辆在四个关键角度的精确TF数据，可用于：
- 更新导航脚本中的角度设置
- 校准车辆的朝向控制
- 优化路径规划算法
