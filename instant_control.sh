#!/bin/bash
# 即时响应的机器人控制脚本

echo "=== 即时机器人控制 ==="
echo "快速响应控制方案"
echo ""

# 设置ROS环境
cd /home/<USER>/ucar_ws
source devel/setup.bash
export ROS_MASTER_URI=http://localhost:11311
export ROS_IP=**************
export ROS_HOSTNAME=**************

echo "控制说明："
echo "W - 前进    S - 后退"
echo "A - 左转    D - 右转"
echo "X - 停止    Q - 退出"
echo ""

# 定义控制函数
forward() {
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.4, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' &
}

backward() {
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: -0.4, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' &
}

left() {
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.8}}' &
}

right() {
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: -0.8}}' &
}

stop_robot() {
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' &
}

# 主控制循环
while true; do
    echo -n "输入命令 (W/S/A/D/X/Q): "
    read -n 1 -s key
    echo ""
    
    case ${key^^} in  # 转换为大写
        W)
            echo "前进..."
            forward
            sleep 0.3
            stop_robot
            ;;
        S)
            echo "后退..."
            backward
            sleep 0.3
            stop_robot
            ;;
        A)
            echo "左转..."
            left
            sleep 0.3
            stop_robot
            ;;
        D)
            echo "右转..."
            right
            sleep 0.3
            stop_robot
            ;;
        X)
            echo "停止..."
            stop_robot
            ;;
        Q)
            echo "退出控制..."
            stop_robot
            break
            ;;
        *)
            echo "无效命令，请使用 W/S/A/D/X/Q"
            ;;
    esac
done

echo "控制结束"
