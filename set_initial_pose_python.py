#!/usr/bin/env python3
"""
使用Python直接发布初始位置，避免rostopic的Cryptodome依赖问题
"""

import rospy
from geometry_msgs.msg import PoseWithCovarianceStamped, PoseWithCovariance, Pose, Point, Quaternion
from std_msgs.msg import Header
import sys
import time

def set_initial_pose():
    """设置机器人初始位置"""
    
    # 初始化ROS节点
    rospy.init_node('set_initial_pose_python', anonymous=True)
    
    # 创建发布者
    pub = rospy.Publisher('/initialpose', PoseWithCovarianceStamped, queue_size=1)
    
    # 等待发布者连接
    rospy.sleep(1.0)
    
    # 创建初始位置消息
    pose_msg = PoseWithCovarianceStamped()
    
    # 设置消息头
    pose_msg.header = Header()
    pose_msg.header.stamp = rospy.Time.now()
    pose_msg.header.frame_id = "map"
    
    # 设置位置
    pose_msg.pose.pose.position = Point()
    pose_msg.pose.pose.position.x = -0.049
    pose_msg.pose.pose.position.y = 0.109
    pose_msg.pose.pose.position.z = 0.0
    
    # 设置朝向 (四元数)
    pose_msg.pose.pose.orientation = Quaternion()
    pose_msg.pose.pose.orientation.x = 0.0
    pose_msg.pose.pose.orientation.y = 0.0
    pose_msg.pose.pose.orientation.z = -0.004
    pose_msg.pose.pose.orientation.w = 1.000
    
    # 设置协方差矩阵
    pose_msg.pose.covariance = [
        0.25, 0.0, 0.0, 0.0, 0.0, 0.0,
        0.0, 0.25, 0.0, 0.0, 0.0, 0.0,
        0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
        0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
        0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
        0.0, 0.0, 0.0, 0.0, 0.0, 0.06853891945200942
    ]
    
    # 发布消息
    print("🎯 设置机器人初始位置...")
    print(f"📍 位置: ({pose_msg.pose.pose.position.x}, {pose_msg.pose.pose.position.y}, {pose_msg.pose.pose.position.z})")
    print(f"🧭 朝向: -0.439度")
    
    # 发布多次确保接收
    for i in range(3):
        pub.publish(pose_msg)
        rospy.sleep(0.5)
    
    print("✅ 初始位置设置完成！")

if __name__ == '__main__':
    try:
        set_initial_pose()
    except rospy.ROSInterruptException:
        print("❌ ROS节点被中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 设置初始位置时出错: {e}")
        sys.exit(1)
