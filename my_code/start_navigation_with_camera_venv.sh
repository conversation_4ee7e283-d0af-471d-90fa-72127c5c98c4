#!/bin/bash
# 启动导航系统和虚拟环境中的摄像头校正节点

# 设置ROS网络环境
source /home/<USER>/ucar_ws/my_code/ros_network_config.sh 192.168.20.115

# 设置ROS环境
source /home/<USER>/ucar_ws/devel/setup.bash

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}===== 启动导航系统和摄像头校正节点 =====${NC}"

# 检查摄像头设备
echo -e "${GREEN}检查摄像头设备...${NC}"
if [ ! -e "/dev/video0" ]; then
    echo -e "${RED}错误: 摄像头设备不存在${NC}"
    exit 1
fi

# 检查校正文件
CALIBRATION_FILE="/home/<USER>/ucar_ws/calibration_result_20250506_213104.npz"
if [ ! -f "$CALIBRATION_FILE" ]; then
    echo -e "${RED}错误: 校正文件不存在: $CALIBRATION_FILE${NC}"
    exit 1
else
    echo -e "${GREEN}找到校正文件: $CALIBRATION_FILE${NC}"
fi

# 检查Python虚拟环境
echo -e "${GREEN}检查Python虚拟环境...${NC}"

# 尝试不同的虚拟环境路径
VENV_PATHS=(
    "/home/<USER>/miniconda3/envs/py38"
    "/home/<USER>/py38_env"
    "/home/<USER>/venv"
    "/home/<USER>/.virtualenvs/py38"
)

VENV_PATH=""
for path in "${VENV_PATHS[@]}"; do
    if [ -d "$path" ]; then
        echo "找到虚拟环境: $path"
        VENV_PATH=$path
        break
    fi
done

if [ -z "$VENV_PATH" ]; then
    echo -e "${YELLOW}未找到预定义的虚拟环境，使用系统Python${NC}"
    USE_VENV=false
else
    echo -e "${GREEN}使用虚拟环境: $VENV_PATH${NC}"
    USE_VENV=true
fi

# 启动导航系统
echo -e "${GREEN}启动导航系统...${NC}"
roslaunch ucar_nav ucar_navigation.launch &
NAVIGATION_PID=$!

# 等待导航系统启动
echo "等待导航系统启动..."
sleep 10

# 启动摄像头
echo -e "${GREEN}启动摄像头...${NC}"
roslaunch usb_cam usb_cam-test.launch &
CAMERA_PID=$!

# 等待摄像头启动
echo "等待摄像头启动..."
sleep 3

# 检查摄像头是否成功启动
if ! rostopic list | grep -q "/usb_cam/image_raw"; then
    echo -e "${RED}错误: 摄像头未成功启动${NC}"
    kill $NAVIGATION_PID $CAMERA_PID 2>/dev/null
    exit 1
fi

# 创建临时脚本来运行校正节点
echo -e "${GREEN}创建临时脚本...${NC}"
if [ "$USE_VENV" = true ]; then
    cat > /tmp/run_calibration_nav.sh << EOF
#!/bin/bash

# 激活虚拟环境
if [ -f "$VENV_PATH/bin/activate" ]; then
    source "$VENV_PATH/bin/activate"
elif [ -f "/home/<USER>/miniconda3/bin/activate" ] && [ -d "/home/<USER>/miniconda3/envs/py38" ]; then
    source "/home/<USER>/miniconda3/bin/activate" py38
else
    echo "无法激活虚拟环境"
    exit 1
fi

# 设置ROS环境
source /home/<USER>/ucar_ws/devel/setup.bash

# 运行校正节点
python /home/<USER>/ucar_ws/my_code/camera_calibration_simple.py
EOF
else
    cat > /tmp/run_calibration_nav.sh << EOF
#!/bin/bash

# 设置ROS环境
source /home/<USER>/ucar_ws/devel/setup.bash

# 运行校正节点
python /home/<USER>/ucar_ws/my_code/camera_calibration_simple.py
EOF
fi

chmod +x /tmp/run_calibration_nav.sh

# 启动校正节点
echo -e "${GREEN}启动校正节点...${NC}"
/tmp/run_calibration_nav.sh &
CALIBRATION_PID=$!

# 等待校正节点启动
echo "等待校正节点启动..."
sleep 5

# 检查校正节点是否成功启动
if ! rostopic list | grep -q "/usb_cam/image_calibrated"; then
    echo -e "${YELLOW}警告: 校正节点可能未成功启动${NC}"
    IMAGE_TOPIC="/usb_cam/image_raw"
else
    echo -e "${GREEN}校正节点已成功启动${NC}"
    IMAGE_TOPIC="/usb_cam/image_calibrated"
fi

# 设置初始位置
echo -e "${GREEN}设置初始位置...${NC}"
rostopic pub -1 /initialpose geometry_msgs/PoseWithCovarianceStamped '{header: {frame_id: "map"}, pose: {pose: {position: {x: -0.049, y: 0.109, z: 0.0}, orientation: {x: 0.0, y: 0.0, z: -0.004, w: 1.000}}, covariance: [0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.06853891945200942]}}'

# 等待初始位置设置生效
sleep 3

# 再次设置初始位置以确保生效
echo -e "${GREEN}再次确认初始位置...${NC}"
rostopic pub -1 /initialpose geometry_msgs/PoseWithCovarianceStamped '{header: {frame_id: "map"}, pose: {pose: {position: {x: -0.049, y: 0.109, z: 0.0}, orientation: {x: 0.0, y: 0.0, z: -0.004, w: 1.000}}, covariance: [0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.06853891945200942]}}'
sleep 2

# 启动图像查看器
echo -e "${GREEN}启动图像查看器...${NC}"
rosrun image_view image_view image:=$IMAGE_TOPIC __name:=image_viewer _window_name:="摄像头图像" &
VIEWER_PID=$!

echo -e "${YELLOW}===== 导航系统和摄像头校正节点已启动 =====${NC}"
echo "使用图像话题: $IMAGE_TOPIC"
echo -e "${YELLOW}按Ctrl+C停止${NC}"

# 捕获Ctrl+C信号，优雅地关闭程序
trap "echo '正在关闭程序...'; kill $VIEWER_PID $CALIBRATION_PID $CAMERA_PID $NAVIGATION_PID 2>/dev/null" EXIT

# 等待用户按Ctrl+C
wait $NAVIGATION_PID
