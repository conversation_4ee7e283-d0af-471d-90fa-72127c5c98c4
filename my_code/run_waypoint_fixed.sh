#!/bin/bash
# 使用正确的IP地址运行导航点导航脚本

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}===== 使用正确的IP地址运行导航点导航脚本 =====${NC}"

# 激活Python虚拟环境
echo -e "${GREEN}激活Python虚拟环境py38...${NC}"
source ~/miniconda3/bin/activate py38
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Python虚拟环境py38激活成功${NC}"
    python --version
else
    echo -e "${RED}Python虚拟环境py38激活失败，将使用系统Python${NC}"
    python --version
fi

# 设置正确的IP地址
IP_ADDRESS="**************"
echo -e "${GREEN}使用IP地址: $IP_ADDRESS${NC}"

# 设置ROS网络环境变量
echo -e "${GREEN}设置ROS网络环境变量...${NC}"
export ROS_IP=$IP_ADDRESS
export ROS_MASTER_URI=http://$IP_ADDRESS:11311
export ROS_HOSTNAME=$IP_ADDRESS

# 显示ROS环境变量
echo -e "${GREEN}ROS环境变量:${NC}"
echo "ROS_IP: $ROS_IP"
echo "ROS_MASTER_URI: $ROS_MASTER_URI"
echo "ROS_HOSTNAME: $ROS_HOSTNAME"

# 设置ROS环境
echo -e "${GREEN}设置ROS环境...${NC}"
source /home/<USER>/ucar_ws/devel/setup.bash

# 检查ROS是否在运行
echo -e "${GREEN}检查ROS是否在运行...${NC}"
if ! pgrep -x "rosmaster" > /dev/null; then
    echo -e "${YELLOW}ROS核心未运行，启动roscore...${NC}"
    roscore &
    ROSCORE_PID=$!
    sleep 5
else
    echo -e "${GREEN}ROS核心已在运行${NC}"
fi

# 检查导航系统是否在运行
echo -e "${GREEN}检查导航系统是否在运行...${NC}"
if ! rostopic list | grep -q "/move_base"; then
    echo -e "${YELLOW}导航系统未运行，启动导航系统...${NC}"
    roslaunch ucar_nav ucar_navigation.launch &
    NAVIGATION_PID=$!
    sleep 10
else
    echo -e "${GREEN}导航系统已在运行${NC}"
fi

# 检查摄像头是否在运行
echo -e "${GREEN}检查摄像头是否在运行...${NC}"
if ! rostopic list | grep -q "/usb_cam/image_raw"; then
    echo -e "${YELLOW}摄像头未运行，启动摄像头...${NC}"
    roslaunch usb_cam usb_cam-test.launch &
    CAMERA_PID=$!
    sleep 3
else
    echo -e "${GREEN}摄像头已在运行${NC}"
fi

# 运行导航点导航脚本
echo -e "${GREEN}运行导航点导航脚本...${NC}"
echo "选择要运行的脚本:"
echo "1. 原始脚本 (waypoint_navigation_array.py)"
echo "2. 调试脚本 (waypoint_navigation_debug.py)"
read -p "请输入选项 (1/2): " choice

case $choice in
    1)
        echo -e "${GREEN}运行原始脚本...${NC}"
        # 使用环境变量运行Python脚本
        ROS_IP=$IP_ADDRESS ROS_MASTER_URI=http://$IP_ADDRESS:11311 ROS_HOSTNAME=$IP_ADDRESS python3 /home/<USER>/ucar_ws/my_code/waypoint_navigation_array.py
        ;;
    2)
        echo -e "${GREEN}运行调试脚本...${NC}"
        # 使用环境变量运行Python脚本
        ROS_IP=$IP_ADDRESS ROS_MASTER_URI=http://$IP_ADDRESS:11311 ROS_HOSTNAME=$IP_ADDRESS python3 /home/<USER>/ucar_ws/my_code/waypoint_navigation_debug.py
        ;;
    *)
        echo -e "${RED}无效选项，默认运行调试脚本...${NC}"
        # 使用环境变量运行Python脚本
        ROS_IP=$IP_ADDRESS ROS_MASTER_URI=http://$IP_ADDRESS:11311 ROS_HOSTNAME=$IP_ADDRESS python3 /home/<USER>/ucar_ws/my_code/waypoint_navigation_debug.py
        ;;
esac

echo -e "${YELLOW}===== 导航点导航脚本执行完成 =====${NC}"
