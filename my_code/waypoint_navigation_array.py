#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import actionlib
from move_base_msgs.msg import MoveBaseAction, MoveBaseGoal
from geometry_msgs.msg import PoseWithCovarianceStamped, PoseStamped, Twist
from std_msgs.msg import String
from tf.transformations import quaternion_from_euler
import math
import time
import os
import sys
import cv2
import numpy as np
from cv_bridge import CvBridge
from sensor_msgs.msg import Image
from pyzbar import pyzbar
from tf import TransformListener
import traceback
import threading
import multiprocessing
import tf
from tf.transformations import euler_from_quaternion
import requests  # Add import for HTTP requests

# 设置PyTorch多线程环境变量（必须在import torch之前设置）
num_cores = multiprocessing.cpu_count()
num_workers = max(2, int(num_cores * 0.8))
os.environ["OMP_NUM_THREADS"] = str(num_workers)
os.environ["MKL_NUM_THREADS"] = str(num_workers)

# PID控制器类
class PIDController:
    """简单的PID控制器"""
    def __init__(self, kp=0.01, ki=0.00, kd=0.00, setpoint=0):
        self.kp = kp  # 比例增益
        self.ki = ki  # 积分增益
        self.kd = kd  # 微分增益
        self.setpoint = setpoint  # 设定值
        self.error_sum = 0  # 误差累积
        self.last_error = 0  # 上一次误差
        self.last_time = time.time()  # 上一次时间
        self.output_limits = (-100, 100)  # 输出限制

    def compute(self, process_variable):
        """计算PID输出"""
        # 计算时间差
        current_time = time.time()
        dt = current_time - self.last_time
        if dt <= 0:
            dt = 0.1  # 防止除零错误

        # 计算误差
        error = self.setpoint - process_variable

        # 计算比例项
        p_term = self.kp * error

        # 计算积分项
        self.error_sum += error * dt
        i_term = self.ki * self.error_sum

        # 计算微分项
        d_term = 0
        if dt > 0:
            d_term = self.kd * (error - self.last_error) / dt

        # 计算总输出
        output = p_term + i_term + d_term

        # 限制输出范围
        if output > self.output_limits[1]:
            output = self.output_limits[1]
        elif output < self.output_limits[0]:
            output = self.output_limits[0]

        # 更新状态
        self.last_error = error
        self.last_time = current_time

        return output, (p_term, i_term, d_term)

# 导入YOLO
try:
    from ultralytics import YOLO
    print("成功导入ultralytics库")
except ImportError:
    print("警告: 未安装ultralytics库，请使用以下命令安装:")
    print("pip install ultralytics")

# 添加更多的调试输出
print("脚本开始执行...")
print("Python版本: {}".format(sys.version))
print("OpenCV版本: {}".format(cv2.__version__))
print("当前工作目录: {}".format(os.getcwd()))

class WaypointNavigation:

    def __init__(self):
        rospy.init_node('waypoint_navigation', anonymous=True)

        # 初始化变量
        self.client = None
        self.init_pose_pub = None
        self.current_waypoint = 0
        self.total_waypoints = 0
        self.waypoints = []
        self.is_navigating = False
        self.bridge = CvBridge()
        self.image_sub = None
        self.qr_result = None
        self.is_scanning_qr = False

        # 红绿灯状态控制
        self.traffic_light_1_is_red = False  # 红绿灯1是否为红灯

        # 白线检测相关变量
        self.is_detecting_white_line = False
        self.white_line_detected = False
        self.calibrated_image_sub = None
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=10)
        self.move_forward_distance = 0.0  # 向前移动的距离（米）

        # 创建action client
        rospy.loginfo("创建action client...")
        try:
            # 尝试连接到不同的move_base服务器名称
            server_names = ['move_base', '/move_base']
            connected = False

            for name in server_names:
                try:
                    self.client = actionlib.SimpleActionClient(name, MoveBaseAction)
                    rospy.loginfo("尝试连接到 %s 服务器..." % name)
                    # 设置超时时间为5秒
                    if self.client.wait_for_server(rospy.Duration(5.0)):
                        rospy.loginfo("已连接到 %s 服务器" % name)
                        connected = True
                        break
                except Exception as e:
                    rospy.logwarn("连接到 %s 服务器失败: %s" % (name, str(e)))

            if not connected:
                rospy.logerr("无法连接到任何move_base服务器")
                # 不要提前返回，继续初始化其他变量
        except Exception as e:
            rospy.logerr("连接move_base服务器时出错: %s" % str(e))

        # 初始位置发布者
        self.init_pose_pub = rospy.Publisher('/initialpose', PoseWithCovarianceStamped, queue_size=1)

        # 当前位置和状态
        self.current_waypoint = 0
        self.total_waypoints = 0
        self.waypoints = []
        self.is_navigating = False

        # 二维码识别相关
        self.bridge = CvBridge()
        self.image_sub = None  # 将在需要时初始化
        self.qr_result = None
        self.is_scanning_qr = False

        # 定义导航点 - 直接使用数组
        # 格式: [x, y, yaw(弧度), actions, delay, time]
        # actions: 可以是单个动作字符串，也可以是动作列表，按顺序执行
        # delay: 执行完动作后的延迟时间（秒）
        # time: 导航到该点的最大时间（秒），超过这个时间就直接前往下一个点
        self.waypoints_array = [
            # 测试航点 - 从当前位置(1.276, 0.462, 170.839°)出发
            # [1.5, 0.7, 2.982, ["detect_white_line", "scan_qrcode_and_set_target"], 0, 30],  # 测试点1，短距离移动，保持相似朝向
            # 原始航点（备用）
            [1.16, 0.41, 3.126, ["detect_white_line", "scan_qrcode_and_set_target"], 0, 30],  # 点1，到达后先检测白线，再扫描二维码，最多等待30秒
            #[-14.1, -0.8, -0.682, ["rotate_360_detect_banana", "center_and_approach_banana", "detect_white_line"], 0, 180],  # 点2，扫描二维码设置目标，旋转检测目标，居中接近，检测白线
            # [-14.1, -0.8, -0.447, ["detect_white_line","xushijiehe"], 0, 180],  # 点3，回到中心位置


            # [-15.922, -0.447,-2.2,"detect_traffic_light",0,30], #红绿灯1
            # [-16.884, 0.516,-2.2,"detect_traffic_light",0,30], #红绿灯2
            # [-15.400, -0.100,1.098,"",0,30],#路口1
            # [-16.891, 1.113,0.550,"",0,30], #路口2


            # [-14.136, -0.902,-2.2,"detect_white_line",0,30],
            # 红绿灯测试
            # [-15.922, -0.147,-2.2,"detect_traffic_light",0,30],
            # [-16.884, 0.516,-2.2,"detect_traffic_light",0,30],

        ]

        # 将数组转换为字典列表
        self.load_waypoints_from_array()

        rospy.loginfo("导航系统初始化完成")

    def load_waypoints_from_array(self):
        """从数组加载导航点"""
        self.waypoints = []

        for point in self.waypoints_array:
            x, y, yaw = point[0], point[1], point[2]
            actions = point[3] if len(point) > 3 else ""
            delay = point[4] if len(point) > 4 else 0
            time = point[5] if len(point) > 5 else 60  # 默认60秒

            # 确保actions是列表形式
            if isinstance(actions, str):
                if actions:  # 如果是非空字符串
                    actions = [actions]  # 转换为单元素列表
                else:
                    actions = []  # 空字符串转换为空列表

            self.waypoints.append({
                'x': x,
                'y': y,
                'yaw': yaw,
                'actions': actions,
                'delay': delay,
                'time': time
            })

        self.total_waypoints = len(self.waypoints)
        self.current_waypoint = 0

        rospy.loginfo("成功加载 %d 个导航点" % self.total_waypoints)
        return True

    def set_initial_pose(self, x, y, yaw):
        """设置初始位置"""
        init_pose = PoseWithCovarianceStamped()
        init_pose.header.stamp = rospy.Time.now()
        init_pose.header.frame_id = "map"

        init_pose.pose.pose.position.x = x
        init_pose.pose.pose.position.y = y
        init_pose.pose.pose.position.z = 0.0

        # 将偏航角转换为四元数
        q = quaternion_from_euler(0, 0, yaw)
        init_pose.pose.pose.orientation.x = q[0]
        init_pose.pose.pose.orientation.y = q[1]
        init_pose.pose.pose.orientation.z = q[2]
        init_pose.pose.pose.orientation.w = q[3]

        # 设置协方差
        init_pose.pose.covariance = [0.25, 0.0, 0.0, 0.0, 0.0, 0.0,
                                    0.0, 0.25, 0.0, 0.0, 0.0, 0.0,
                                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                    0.0, 0.0, 0.0, 0.0, 0.0, 0.06853891945200942]

        # 发布初始位置
        rospy.loginfo("设置初始位置: x=%f, y=%f, yaw=%f" % (x, y, yaw))
        self.init_pose_pub.publish(init_pose)
        rospy.sleep(1)  # 等待位置更新

    def send_goal(self, x, y, yaw):
        """发送导航目标"""
        goal = MoveBaseGoal()
        goal.target_pose.header.frame_id = "map"
        goal.target_pose.header.stamp = rospy.Time.now()

        goal.target_pose.pose.position.x = x
        goal.target_pose.pose.position.y = y
        goal.target_pose.pose.position.z = 0.0

        # 将偏航角转换为四元数
        q = quaternion_from_euler(0, 0, yaw)
        goal.target_pose.pose.orientation.x = q[0]
        goal.target_pose.pose.orientation.y = q[1]
        goal.target_pose.pose.orientation.z = q[2]
        goal.target_pose.pose.orientation.w = q[3]

        rospy.loginfo("导航到: x=%f, y=%f, yaw=%f" % (x, y, yaw))
        self.client.send_goal(goal)

        return goal

    def navigate_to_waypoint(self, index):
        """导航到指定索引的导航点"""
        if index < 0 or index >= self.total_waypoints:
            rospy.logerr("导航点索引超出范围: %d" % index)
            return False

        # 检查move_base服务器是否可用
        if self.client is None:
            rospy.logerr("move_base客户端未初始化")
            return False

        waypoint = self.waypoints[index]
        self.current_waypoint = index

        try:
            # 发送导航目标
            self.send_goal(waypoint['x'], waypoint['y'], waypoint['yaw'])

            # 等待导航完成，使用指定的最大时间
            self.is_navigating = True
            max_time = waypoint['time']  # 使用指定的最大时间
            rospy.loginfo("导航到点 %d，最大时间: %d 秒" % (index+1, max_time))

            # 使用更宽松的判断条件，在指定时间内完成导航
            result = self.client.wait_for_result(rospy.Duration(max_time))
            self.is_navigating = False

            # 无论是否到达目标点，都认为导航成功
            rospy.loginfo("导航点 %d 导航完成" % (index+1))
            if not result:
                rospy.logwarn("导航点 %d 超时，将继续前往下一个点" % (index+1))
                # 取消当前导航目标
                self.client.cancel_goal()

            # 检查是否需要在此位置播放音频
            if hasattr(self, 'should_play_audio_at_destination') and self.should_play_audio_at_destination:
                rospy.loginfo("到达目标位置，播放音频")
                print("\n" + "="*50)
                print("【目标位置音频】: 到达目标位置，播放音频")
                print("="*50 + "\n")

                # 播放音频
                self.play_audio_file(self.destination_audio_file, self.destination_audio_description)

                # 重置音频播放标志
                self.should_play_audio_at_destination = False
                self.destination_audio_file = ""
                self.destination_audio_description = ""

            # 检查是否到达路口1或路口2，如果是则运行线跟踪脚本
            if index == 4 or index == 5:  # 索引4是路口1，索引5是路口2
                intersection_name = "路口1" if index == 4 else "路口2"
                rospy.loginfo("到达%s，启动线跟踪程序" % intersection_name)
                print("\n" + "="*50)
                print("【线跟踪启动】: 到达%s，启动线跟踪程序" % intersection_name)
                print("="*50 + "\n")

                # 运行线跟踪脚本
                self.run_line_following_script(intersection_name)

            # 执行额外的动作（如果有）
            if waypoint['actions'] and len(waypoint['actions']) > 0:
                rospy.loginfo("开始执行 %d 个任务..." % len(waypoint['actions']))
                for i, action in enumerate(waypoint['actions']):
                    rospy.loginfo("执行任务 %d/%d: %s" % (i+1, len(waypoint['actions']), action))
                    self.execute_action(action)
                rospy.loginfo("所有任务执行完成")

            # 等待指定的延迟时间
            if waypoint['delay'] > 0:
                rospy.loginfo("等待 %d 秒" % waypoint['delay'])
                rospy.sleep(waypoint['delay'])

            return True
        except Exception as e:
            rospy.logerr("导航到导航点 %d 时出错: %s" % (index+1, str(e)))
            self.is_navigating = False
            return False

    def navigate_all_waypoints(self):
        """根据红绿灯状态导航所有导航点"""
        if self.total_waypoints == 0:
            rospy.logerr("没有导航点可导航")
            return False

        rospy.loginfo("开始导航所有 %d 个导航点" % self.total_waypoints)

        # 初始化next_waypoint属性和完成标志
        self.next_waypoint = 0
        self.navigation_complete_after_next = False

        # 最大循环次数，防止无限循环
        max_iterations = 10
        iteration_count = 0

        success = True
        while iteration_count < max_iterations:
            iteration_count += 1

            # 获取当前要导航的点
            i = self.next_waypoint

            # 检查是否已经完成所有导航点
            if i >= self.total_waypoints:
                rospy.loginfo("已完成所有导航点")
                break

            rospy.loginfo(f"导航到点 {i+1}/{self.total_waypoints} (索引: {i})")

            # 保存当前导航点索引，用于检测是否被修改
            current_next_waypoint = self.next_waypoint

            # 导航到当前点
            if not self.navigate_to_waypoint(i):
                success = False
                break

            # 检查是否设置了完成标志
            should_end_after_next = False
            if hasattr(self, 'navigation_complete_after_next') and self.navigation_complete_after_next:
                should_end_after_next = True
                rospy.loginfo("检测到完成标志，将在下一个导航点后结束导航")
                print("\n" + "="*50)
                print("【导航状态】: 将在下一个导航点后结束导航")
                print("="*50 + "\n")

            # 如果当前点是最后一个点，结束导航
            if i == self.total_waypoints - 1:
                rospy.loginfo("已到达最后一个导航点")
                break

            # 检查next_waypoint是否被修改（例如被红绿灯检测函数修改）
            if self.next_waypoint == current_next_waypoint:
                # 如果没有被修改，默认导航到下一个点
                self.next_waypoint = i + 1
                rospy.loginfo(f"默认设置下一个导航点为: {self.next_waypoint}")
            else:
                # 如果被修改，使用修改后的值
                rospy.loginfo(f"检测到next_waypoint被修改为: {self.next_waypoint}")

            # 如果设置了完成标志，并且已经导航到了当前点，那么导航到下一个点后结束
            if should_end_after_next:
                # 保存下一个导航点索引
                next_point = self.next_waypoint

                # 导航到下一个点
                rospy.loginfo(f"导航到最终点 {next_point+1}/{self.total_waypoints} (索引: {next_point})")
                if not self.navigate_to_waypoint(next_point):
                    success = False
                else:
                    rospy.loginfo("已到达最终导航点，导航结束")
                    print("\n" + "="*50)
                    print("【导航完成】: 根据红绿灯状态，导航任务结束")
                    print("="*50 + "\n")

                # 导航结束
                break

            print(f"\n{'='*50}")
            print(f"【导航状态】: 当前点 {i+1}/{self.total_waypoints} (索引: {i})")
            print(f"【下一个点】: {self.next_waypoint+1}/{self.total_waypoints} (索引: {self.next_waypoint})")
            print(f"【迭代计数】: {iteration_count}/{max_iterations}")
            print(f"【完成标志】: {'是' if hasattr(self, 'navigation_complete_after_next') and self.navigation_complete_after_next else '否'}")
            print(f"{'='*50}\n")

        if success:
            rospy.loginfo("所有导航点导航完成")
        else:
            rospy.logerr("导航在导航点 %d 处失败" % (self.current_waypoint+1))

        return success

    def execute_action(self, action):
        """执行额外的动作"""
        rospy.loginfo("执行动作: %s" % action)

        # 这里可以根据action字符串执行不同的动作
        # 例如: 拍照、抓取物体、播放声音等

        # 示例: 简单的延迟
        if action == "wait":
            rospy.sleep(2)

        # 示例: 打印消息
        elif action == "speak":
            rospy.loginfo("我到达了目标点!")

        # 在这里添加更多的动作...

        # 自定义动作1: 拍照
        elif action == "take_photo":
            self.take_photo()

        # 自定义动作2: 播放声音
        elif action == "play_sound":
            self.play_sound()

        # 自定义动作3: 扫描二维码
        elif action == "scan_qrcode":
            self.scan_qrcode()
            
        # 自定义动作3.1: 扫描二维码并设置检测目标
        elif action == "scan_qrcode_and_set_target":
            self.scan_qrcode_and_set_target()

        # 自定义动作4: 检测白线并向前移动
        elif action == "detect_white_line":
            self.detect_white_line()
            # self.scan_qrcode()

        # 自定义动作5: 原地旋转360°并检测目标物体
        elif action == "rotate_360_detect_banana":
            # 如果没有预先设置目标类别，使用默认值（香蕉）
            if not hasattr(self, 'target_class_ids'):
                self.target_class_ids = [3]  # 默认检测香蕉
                self.target_class_name = "香蕉"
                
            self.rotate_360_detect_banana()
            
        # 自定义动作7: 将目标物体居中并前进到适当距离
        elif action == "center_and_approach_banana":
            # 如果没有预先设置目标类别，使用默认值（香蕉）
            if not hasattr(self, 'target_class_ids'):
                self.target_class_ids = [3]  # 默认检测香蕉
                self.target_class_name = "香蕉"
                
            self.center_and_approach_banana()
            
        # 自定义动作8: 前进固定距离
        elif action == "move_forward_10cm":
            self.move_forward_fixed_distance(0.1, 0.1)  # 前进10cm，速度0.1m/s
            
        # 自定义动作9: 前进固定距离（可配置）
        elif action.startswith("move_forward_"):
            # 解析距离参数，格式: move_forward_XXcm
            try:
                # 提取距离值，例如从"move_forward_10cm"提取"10"
                distance_str = action.replace("move_forward_", "").replace("cm", "")
                distance = float(distance_str) / 100.0  # 转换为米
                rospy.loginfo(f"解析到前进距离: {distance}米")
                self.move_forward_fixed_distance(distance, 0.1)  # 使用固定速度0.1m/s
            except ValueError as e:
                rospy.logerr(f"解析前进距离参数失败: {e}")
                rospy.logerr(f"动作格式应为: move_forward_XXcm，例如: move_forward_10cm")
                
        # 自定义动作10: 虚实结合 - 与后端服务器通信
        elif action == "xushijiehe":
            self.xushijiehe()

        # 自定义动作6: 检测红绿灯
        elif action == "detect_traffic_light":
            # 检查当前是否在红绿灯2位置且红绿灯1是红灯
            if self.current_waypoint == 3 and self.traffic_light_1_is_red:  # 索引3是红绿灯2
                # 红绿灯1是红灯时，红绿灯2位置不检测，直接播放音频并导航到路口2
                rospy.loginfo("红绿灯1是红灯，红绿灯2位置跳过检测，直接播放路口2音频并导航到路口2")
                print("\n" + "="*50)
                print("【红绿灯2逻辑】: 红绿灯1是红灯，跳过红绿灯2检测")
                print("【决策】: 直接播放路口2音频并导航到路口2")
                print("="*50 + "\n")

                # 播放路口2音频
                self.play_audio_file("/home/<USER>/ucar_ws/my_code/voice/路口2.wav", "路口2")

                # 设置下一个导航点为路口2
                self.next_waypoint = 5  # 索引5对应路口2
                self.navigation_complete_after_next = True  # 设置完成标志

                print("\n" + "="*50)
                print(f"【导航设置】: 下一个导航点 = {self.next_waypoint} (路口2)")
                print(f"【完成标志】: {'是' if self.navigation_complete_after_next else '否'}")
                print("="*50 + "\n")
            else:
                # 正常的红绿灯检测逻辑
                result = self.detect_traffic_light()
                rospy.loginfo(f"红绿灯检测结果: {result}")
                print("\n" + "="*50)
                print(f"【红绿灯检测】: 检测结果 = {result}")
                print(f"【下一个导航点】: {self.next_waypoint}")

                # 检查是否设置了完成标志
                if hasattr(self, 'navigation_complete_after_next'):
                    print(f"【完成标志】: {'是' if self.navigation_complete_after_next else '否'}")
                else:
                    print("【完成标志】: 未设置")

                print("="*50 + "\n")

        # 未知动作
        else:
            rospy.logwarn("未知动作: %s" % action)

    # 以下是可以扩展的功能方法

    def take_photo(self):
        """拍照功能"""
        rospy.loginfo("拍照...")
        # 这里添加拍照的代码
        # 例如: 调用相机节点拍照并保存
        try:
            # 示例代码，需要根据实际相机进行修改
            rospy.loginfo("拍照完成")
        except Exception as e:
            rospy.logerr("拍照失败: %s" % str(e))

    def play_sound(self):
        """播放声音功能"""
        rospy.loginfo("播放声音...")
        # 这里添加播放声音的代码
        try:
            # 示例代码，需要根据实际声音设备进行修改
            os.system("aplay /path/to/sound.wav")
            rospy.loginfo("播放声音完成")
        except Exception as e:
            rospy.logerr("播放声音失败: %s" % str(e))

    def scan_qrcode(self):
        """二维码识别功能"""
        rospy.loginfo("开始扫描二维码...")

        # 设置扫描状态
        self.is_scanning_qr = True
        self.qr_result = None

        # 订阅相机图像话题
        self.image_sub = rospy.Subscriber('/usb_cam/image_raw', Image, self.image_callback)

        # 等待扫描结果，最多等待10秒
        start_time = rospy.Time.now()
        timeout = rospy.Duration(10.0)  # 10秒超时
        rate = rospy.Rate(3)  # 3Hz

        while self.is_scanning_qr and (rospy.Time.now() - start_time) < timeout:
            if self.qr_result:
                rospy.loginfo("扫描到二维码: %s" % self.qr_result)
                break
            rate.sleep()

        # 取消订阅
        if self.image_sub:
            self.image_sub.unregister()
            self.image_sub = None

        # 检查结果并播放相应音频
        if self.qr_result:
            self.play_audio_based_on_qr_result(self.qr_result)
        else:
            rospy.logwarn("未扫描到二维码或超时")

        # 重置扫描状态
        self.is_scanning_qr = False

    def play_audio_based_on_qr_result(self, qr_content):
        """根据二维码内容播放相应的音频文件"""
        rospy.loginfo("根据二维码内容播放音频: %s" % qr_content)

        # 定义音频文件路径映射
        audio_files = {
            "Fruit": "/home/<USER>/ucar_ws/my_code/voice/1_Fruit.wav",
            "Dessert": "/home/<USER>/ucar_ws/my_code/voice/1_Dessert.wav",
            "Vegetable": "/home/<USER>/ucar_ws/my_code/voice/1_Vegetable.wav"
        }

        # 清理二维码内容（去除空格和换行符）
        qr_content = qr_content.strip()

        # 检查是否有对应的音频文件
        if qr_content in audio_files:
            audio_file = audio_files[qr_content]

            # 检查音频文件是否存在
            if os.path.exists(audio_file):
                rospy.loginfo("播放音频文件: %s" % audio_file)
                print(f"【音频播放】: 检测到 {qr_content}，播放音频文件: {audio_file}")

                try:
                    # 使用aplay播放WAV文件
                    os.system("aplay %s" % audio_file)
                    rospy.loginfo("音频播放完成")
                    print(f"【音频播放】: {qr_content} 音频播放完成")
                except Exception as e:
                    rospy.logerr("播放音频失败: %s" % str(e))
                    print(f"【音频播放】: 播放失败 - {str(e)}")
            else:
                rospy.logwarn("音频文件不存在: %s" % audio_file)
                print(f"【音频播放】: 音频文件不存在 - {audio_file}")
        else:
            rospy.logwarn("未识别的二维码内容，无对应音频文件: %s" % qr_content)
            print(f"【音频播放】: 未识别的二维码内容 - {qr_content}")
            print(f"【音频播放】: 支持的内容: {list(audio_files.keys())}")

    def play_audio_file(self, audio_file_path, description="音频"):
        """播放指定的音频文件"""
        rospy.loginfo("播放%s音频: %s" % (description, audio_file_path))
        print(f"【音频播放】: 播放{description}音频: {audio_file_path}")

        # 检查音频文件是否存在
        if os.path.exists(audio_file_path):
            try:
                # 使用aplay播放WAV文件
                result = os.system("aplay %s" % audio_file_path)
                if result == 0:
                    rospy.loginfo("%s音频播放完成" % description)
                    print(f"【音频播放】: {description}音频播放完成")
                else:
                    rospy.logerr("%s音频播放失败，返回码: %d" % (description, result))
                    print(f"【音频播放】: {description}音频播放失败，返回码: {result}")
            except Exception as e:
                rospy.logerr("播放%s音频失败: %s" % (description, str(e)))
                print(f"【音频播放】: 播放{description}音频失败 - {str(e)}")
        else:
            rospy.logwarn("%s音频文件不存在: %s" % (description, audio_file_path))
            print(f"【音频播放】: {description}音频文件不存在 - {audio_file_path}")

    def run_line_following_script(self, intersection_name):
        """运行线跟踪脚本"""
        import os
        import subprocess

        rospy.loginfo("在%s启动线跟踪程序" % intersection_name)
        print(f"【线跟踪】: 在{intersection_name}启动线跟踪程序")

        # 线跟踪脚本路径
        script_path = "/home/<USER>/ucar_ws/my_code/run_line_following.sh"

        # 检查脚本是否存在
        if os.path.exists(script_path):
            try:
                rospy.loginfo("执行线跟踪脚本: %s" % script_path)
                print(f"【线跟踪】: 执行脚本: {script_path}")

                # 使用nohup确保脚本在后台独立运行，不受父进程影响

                print(f"【线跟踪】: 正在启动线跟踪程序...")
                print(f"【线跟踪】: 脚本将在独立进程中运行")
                print(f"【线跟踪】: 导航程序结束后，线跟踪将继续运行")

                # 构建命令：使用nohup确保进程独立运行
                cmd = f"cd /home/<USER>/ucar_ws && nohup {script_path} > /tmp/line_following.log 2>&1 &"

                # 启动独立进程
                result = os.system(cmd)

                rospy.loginfo("线跟踪脚本已启动（独立进程）")
                print(f"【线跟踪】: 脚本已启动（独立进程）")
                print(f"【线跟踪】: 命令: {cmd}")
                print(f"【线跟踪】: 日志文件: /tmp/line_following.log")
                print(f"【线跟踪】: 导航任务完成，线跟踪程序接管控制")

                # 等待一小段时间确保脚本启动
                rospy.sleep(3)

                # 检查线跟踪进程是否在运行
                check_cmd = "ps aux | grep -v grep | grep run_line_following"
                check_result = os.system(check_cmd + " > /dev/null 2>&1")

                if check_result == 0:
                    print(f"【线跟踪】: ✅ 线跟踪程序运行正常")
                    rospy.loginfo("线跟踪程序运行正常")
                else:
                    print(f"【线跟踪】: ❌ 警告 - 未检测到线跟踪进程")
                    rospy.logwarn("未检测到线跟踪进程")

                # 显示如何查看线跟踪状态
                print(f"【线跟踪】: 查看线跟踪状态: tail -f /tmp/line_following.log")
                print(f"【线跟踪】: 停止线跟踪: pkill -f run_line_following")

            except Exception as e:
                rospy.logerr("启动线跟踪脚本失败: %s" % str(e))
                print(f"【线跟踪】: 启动失败 - {str(e)}")
        else:
            rospy.logwarn("线跟踪脚本不存在: %s" % script_path)
            print(f"【线跟踪】: 脚本文件不存在 - {script_path}")

    def image_callback(self, msg):
        """相机图像回调函数，用于二维码识别"""
        if not self.is_scanning_qr:
            return

        try:
            # 将ROS图像转换为OpenCV格式
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")

            # 使用pyzbar进行二维码识别
            barcodes = pyzbar.decode(cv_image)

            # 处理识别结果
            for barcode in barcodes:
                # 提取二维码数据
                barcode_data = barcode.data.decode("utf-8")
                barcode_type = barcode.type

                # 在图像上标记二维码
                (x, y, w, h) = barcode.rect
                cv2.rectangle(cv_image, (x, y), (x + w, y + h), (0, 255, 0), 2)

                # 显示二维码类型和数据
                text = "%s: %s" % (barcode_type, barcode_data)
                cv2.putText(cv_image, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                # 保存结果
                self.qr_result = barcode_data
                self.is_scanning_qr = False  # 停止扫描

                rospy.loginfo("识别到%s: %s" % (barcode_type, barcode_data))

            # 显示图像（可选）
            # cv2.imshow("QR Code Scanner", cv_image)
            # cv2.waitKey(1)

        except Exception as e:
            rospy.logerr("处理图像时出错: %s" % str(e))

    def calibrated_image_callback(self, msg):
        """校正后的相机图像回调函数，用于白线检测"""
        if not self.is_detecting_white_line:
            return

        try:
            # 将ROS图像转换为NumPy数组
            img_data = np.frombuffer(msg.data, dtype=np.uint8).reshape(msg.height, msg.width, -1)

            # 转换为灰度图
            gray = cv2.cvtColor(img_data, cv2.COLOR_BGR2GRAY)

            # 使用自适应阈值处理，更好地检测细线
            binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                          cv2.THRESH_BINARY, 11, 2)

            # 获取图像下方区域（假设白线在图像下方）
            height, width = binary.shape
            bottom_region = binary[int(height*0.5):int(height*0.95), :]  # 只关注下方20%的区域

            # 使用形态学操作增强横线
            kernel = np.ones((1, 15), np.uint8)  # 横向kernel，增强横线
            enhanced = cv2.morphologyEx(bottom_region, cv2.MORPH_CLOSE, kernel)

            # 使用霍夫变换检测直线
            edges = cv2.Canny(enhanced, 50, 150, apertureSize=3)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                                   minLineLength=width*0.3, maxLineGap=20)

            # 判断是否检测到横线
            has_horizontal_line = False
            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    # 计算线的角度
                    angle = np.abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)
                    # 如果角度接近0或180度，认为是横线
                    if angle < 40 or angle > 140:
                        has_horizontal_line = True
                        # 在原图上绘制检测到的线（用于调试）
                        # cv2.line(img_data, (x1, y1 + int(height*0.7)), (x2, y2 + int(height*0.7)), (0, 0, 255), 2)
                        rospy.loginfo("检测到横线，角度: %.2f度", angle)
                        break

            # 计算下方区域中白色像素的比例（作为辅助判断）
            white_pixel_ratio = np.sum(bottom_region == 255) / (bottom_region.shape[0] * bottom_region.shape[1])

            # 显示处理后的图像（可选）
            # cv2.imshow("White Line Detection", enhanced)
            # cv2.waitKey(1)

            # 添加连续未检测计数器（如果不存在）
            if not hasattr(self, 'consecutive_no_detection'):
                self.consecutive_no_detection = 0

            # 判断是否检测到白线（优先使用霍夫变换的结果）
            if has_horizontal_line:
                rospy.loginfo("通过霍夫变换检测到横线")
                self.white_line_detected = True
                self.consecutive_no_detection = 0  # 重置计数器
            # elif white_pixel_ratio > 0.1:  # 如果白色像素比例大于10%，作为备选判断
            #     rospy.loginfo("通过像素比例检测到白线: %.2f%%", white_pixel_ratio * 100)
            #     self.white_line_detected = True
            #     self.consecutive_no_detection = 0  # 重置计数器
            else:
                rospy.loginfo("未检测到白线，白色像素比例: %.2f%% (连续未检测次数: %d/5)",
                             white_pixel_ratio * 100, self.consecutive_no_detection + 1)

                # 增加连续未检测计数
                self.consecutive_no_detection += 1

                # 只有连续5次未检测到白线，才将white_line_detected设置为False
                if self.consecutive_no_detection >= 5:
                    rospy.loginfo("已连续5次未检测到白线，确认没有白线")
                    self.white_line_detected = False
                    self.consecutive_no_detection = 0  # 重置计数器

        except Exception as e:
            rospy.logerr("处理图像时出错: %s" % str(e))

    def detect_white_line(self):
        """白线检测功能"""
        rospy.loginfo("开始检测横向白线...")

        # 设置检测状态
        self.is_detecting_white_line = True
        self.white_line_detected = False

        # 订阅校正后的相机图像话题
        self.calibrated_image_sub = rospy.Subscriber('/usb_cam/image_calibrated', Image, self.calibrated_image_callback)

        # 等待检测结果，最多等待8秒
        start_time = rospy.Time.now()
        timeout = rospy.Duration(8.0)  # 8秒超时
        rate = rospy.Rate(10)  # 10Hz

        # 检测计数器
        detection_count = 0
        positive_count = 0

        # 进行多次检测，提高准确性
        while (rospy.Time.now() - start_time) < timeout and detection_count < 5:
            detection_count += 1
            if self.white_line_detected:
                positive_count += 1
            rospy.loginfo("检测 %d/5: %s", detection_count, "检测到白线" if self.white_line_detected else "未检测到白线")
            rate.sleep()

        # 取消订阅
        if self.calibrated_image_sub:
            self.calibrated_image_sub.unregister()
            self.calibrated_image_sub = None

        # 重置检测状态
        self.is_detecting_white_line = False

        # 根据检测结果控制小车向前移动
        if positive_count >= 2:  # 如果至少有2次检测到白线，认为存在白线
            rospy.loginfo("检测到横向白线 (%d/%d)，小车向前移动直到没有白线...", positive_count, detection_count)
            self.white_line_detected = True
            self.move_forward_until_no_white_line()
        else:
            rospy.loginfo("未检测到横向白线 (%d/%d)，继续执行下一个任务", positive_count, detection_count)

    def move_forward_until_no_white_line(self):
        """控制小车向前移动直到没有白线"""
        # 设置检测状态
        self.is_detecting_white_line = True
        self.white_line_detected = True

        # 订阅校正后的相机图像话题
        self.calibrated_image_sub = rospy.Subscriber('/usb_cam/image_calibrated', Image, self.calibrated_image_callback)

        # 等待一段时间，确保获取到最新的图像
        rospy.sleep(1.0)

        # 如果没有检测到白线，不需要移动
        if not self.white_line_detected:
            rospy.loginfo("未检测到白线，不需要移动")

            # 取消订阅
            if self.calibrated_image_sub:
                self.calibrated_image_sub.unregister()
                self.calibrated_image_sub = None

            # 重置检测状态
            self.is_detecting_white_line = False
            return

        rospy.loginfo("检测到白线，开始向前移动...")

        # 创建速度消息
        cmd_vel = Twist()
        cmd_vel.linear.x = 0.05  # 设置线速度为0.05 m/s，更慢更精确

        # 设置超时时间，防止无限移动
        start_time = rospy.Time.now()
        timeout = rospy.Duration(20.0)  # 20秒超时
        rate = rospy.Rate(10)  # 10Hz

        # 记录起始位置
        self.move_forward_distance = 0.0
        last_time = rospy.Time.now()

        # 向前移动直到没有白线或超时
        consecutive_no_line = 0  # 连续几次没有检测到白线

        while (rospy.Time.now() - start_time) < timeout:
            # 发布速度命令
            self.cmd_vel_pub.publish(cmd_vel)

            # 计算移动距离
            current_time = rospy.Time.now()
            dt = (current_time - last_time).to_sec()
            self.move_forward_distance += cmd_vel.linear.x * dt
            last_time = current_time

            # 显示移动距离
            if int(self.move_forward_distance * 100) % 5 == 0:  # 每移动5cm显示一次
                rospy.loginfo("已向前移动: %.2f 米", self.move_forward_distance)

            # 检查是否已经越过白线
            if not self.white_line_detected:
                consecutive_no_line += 1
                rospy.loginfo("未检测到白线 (%d/3)", consecutive_no_line)
                if consecutive_no_line >= 3:  # 连续3次未检测到白线，认为已经越过
                    rospy.loginfo("已连续3次未检测到白线，认为已越过白线")
                    break
            else:
                consecutive_no_line = 0  # 重置计数器
                rospy.loginfo("仍然检测到白线")

            rate.sleep()

        # 再向前移动一小段距离，确保完全越过白线
        extra_distance = 0.00  # 额外移动10cm
        extra_start_time = rospy.Time.now()

        rospy.loginfo("额外向前移动 %.2f 米...", extra_distance)

        while (rospy.Time.now() - extra_start_time).to_sec() < (extra_distance / cmd_vel.linear.x):
            self.cmd_vel_pub.publish(cmd_vel)
            rate.sleep()

        # 停止小车
        cmd_vel.linear.x = 0.0
        self.cmd_vel_pub.publish(cmd_vel)

        # 取消订阅
        if self.calibrated_image_sub:
            self.calibrated_image_sub.unregister()
            self.calibrated_image_sub = None

        # 重置检测状态
        self.is_detecting_white_line = False

        # 显示结果
        if (rospy.Time.now() - start_time) >= timeout:
            rospy.loginfo("超时停止，总共向前移动: %.2f 米", self.move_forward_distance + extra_distance)
        else:
            rospy.loginfo("已移动到没有白线的位置，总共向前移动: %.2f 米", self.move_forward_distance + extra_distance)



    def rotate_360_detect_banana(self):
        """原地旋转360°并检测目标物体，检测到后停止旋转"""
        # 获取目标类别信息
        target_class_ids = self.target_class_ids if hasattr(self, 'target_class_ids') else [3]  # 默认检测香蕉
        target_class_name = self.target_class_name if hasattr(self, 'target_class_name') else "香蕉"
        
        rospy.loginfo(f"准备开始{target_class_name}检测...")
        print(f"\n{'='*50}")
        print(f"【任务】: 旋转360°检测{target_class_name}")
        print(f"【目标类别】: {target_class_ids}")
        print(f"{'='*50}\n")

        # 创建速度消息
        cmd_vel = Twist()
        cmd_vel.angular.z = 0.4  # 降低角速度为0.4 rad/s，使旋转更平稳且容易处理图像

        # 设置超时时间
        timeout = rospy.Duration(180.0)  # 增加到180秒超时，确保有足够时间完成3圈旋转
        rate = rospy.Rate(10)  # 10Hz

        # 目标检测标志
        object_detected = False
        banana_detected = False  # 初始化检测标志

        # 设置旋转圈数
        target_rotations = 3  # 旋转3圈
        target_angle = 2 * math.pi * target_rotations  # 目标角度为3圈(1080度)
        max_detections = 6 * target_rotations  # 每圈6个检测点，共3圈
        
        # 打印旋转计划
        rospy.loginfo(f"计划旋转{target_rotations}圈({target_angle * 180 / math.pi}度)寻找{target_class_name}")
        print(f"\n{'='*50}")
        print(f"【旋转计划】: 将旋转{target_rotations}圈({target_angle * 180 / math.pi}度)寻找{target_class_name}")
        print(f"【检测角度】: 每圈在以下角度检测: {', '.join([f'{angle}°' for angle in [0, 60,120, 180,240,300]])}")
        print(f"【检测频率】: 每圈6次，共{max_detections}次检测")
        print(f"{'='*50}\n")

        try:
            # 加载YOLO模型
            rospy.loginfo("加载YOLO模型...")
            print("正在加载目标检测模型，请稍候...")

            # 尝试多个可能的模型路径
            model_paths = [
                '/home/<USER>/ucar_ws/my_code/best.pt',  # 主要路径
                '/home/<USER>/ucar_ws/best.pt',          # 备用路径1
                '/home/<USER>/best.pt',                  # 备用路径2
            ]

            model_path = None
            for path in model_paths:
                if os.path.exists(path):
                    model_path = path
                    rospy.loginfo("找到模型文件: %s", model_path)
                    print(f"找到模型文件: {model_path}")
                    break

            # 检查模型文件是否存在
            if model_path is None:
                rospy.logerr("未找到任何模型文件，尝试过以下路径: %s", model_paths)
                print("错误: 未找到任何模型文件!")
                return False

            try:
                # 设置多线程环境变量
                if not hasattr(self, 'cpu_cores_setup_done'):
                    num_cores = multiprocessing.cpu_count()
                    self.num_workers = max(2, int(num_cores * 0.8))  # 使用80%的CPU核心
                    os.environ["OMP_NUM_THREADS"] = str(self.num_workers)
                    os.environ["MKL_NUM_THREADS"] = str(self.num_workers)
                    self.cpu_cores_setup_done = True
                    rospy.loginfo(f"多核设置完成: 检测到{num_cores}个核心，使用{self.num_workers}个核心进行处理")
                    print(f"【多核设置】: 检测到{num_cores}个核心，使用{self.num_workers}个核心")
                else:
                    rospy.loginfo(f"使用已设置的多核配置: {self.num_workers}个核心")

                # 加载模型
                rospy.loginfo("开始加载模型: %s", model_path)
                print(f"开始加载模型: {model_path}")
                
                # 确保模型加载时使用多线程
                model = YOLO(model_path)

                # 配置模型参数
                model.conf = 0.75  # 置信度阈值
                model.iou = 0.45   # IoU阈值

                # 获取模型支持的类别
                class_names = model.names
                rospy.loginfo("模型支持的类别: %s", class_names)
                print(f"模型支持的类别: {class_names}")

                # 设置要检测的目标类别
                model.classes = target_class_ids  # 使用动态设置的目标类别
                rospy.loginfo(f"设置模型检测类别: {target_class_ids} ({target_class_name})")
                print(f"【检测设置】: 检测类别={target_class_ids} ({target_class_name})")

                # 预热模型
                rospy.loginfo("开始预热模型...")
                print("开始预热模型，这可能需要几秒钟...")

                # 创建一个更大的测试图像进行预热
                dummy_img = np.zeros((640, 640, 3), dtype=np.uint8)

                # 多次预热以确保模型完全加载
                for i in range(3):
                    rospy.loginfo(f"预热模型 {i+1}/3...")
                    results = model.predict(dummy_img, verbose=False, workers=self.num_workers)
                    rospy.sleep(0.5)  # 短暂暂停确保预热完成

                rospy.loginfo("模型加载完成!")
                print("模型加载和预热完成，准备开始旋转检测目标...")

            except Exception as e:
                rospy.logerr("加载模型时出错: %s", str(e))
                rospy.logerr(traceback.format_exc())
                return False

            # 创建图像订阅者
            self.bridge = CvBridge()
            self.latest_frame = None
            self.frame_received = False

            # 订阅图像话题
            rospy.loginfo("订阅图像话题...")

            # 检查可用的图像话题
            available_topics = rospy.get_published_topics()
            image_topics = [topic for topic, topic_type in available_topics if topic_type == 'sensor_msgs/Image']

            # 优先使用校正后的图像话题
            if '/usb_cam/image_calibrated' in image_topics:
                image_topic = '/usb_cam/image_calibrated'
                rospy.loginfo("使用校正后的图像话题: %s", image_topic)
            elif '/usb_cam/image_raw' in image_topics:
                image_topic = '/usb_cam/image_raw'
                rospy.loginfo("使用原始图像话题: %s", image_topic)
            elif image_topics:
                image_topic = image_topics[0]
                rospy.loginfo("使用可用的图像话题: %s", image_topic)
            else:
                rospy.logerr("未找到任何图像话题，无法进行检测")
                return False

            # 定义图像回调函数
            def image_callback(msg):
                try:
                    # 将ROS图像转换为OpenCV格式
                    frame = self.bridge.imgmsg_to_cv2(msg, "bgr8")

                    # 检查图像是否有效
                    if frame is not None and frame.size > 0:
                        # 检查图像尺寸
                        height, width = frame.shape[:2]
                        if height > 0 and width > 0:
                            self.latest_frame = frame
                            self.frame_received = True
                            # 添加时间戳，用于检查图像是否更新
                            self.latest_frame_time = time.time()
                        else:
                            rospy.logwarn("收到无效图像，尺寸: %dx%d", width, height)
                    else:
                        rospy.logwarn("收到空图像")
                except Exception as e:
                    rospy.logerr("处理图像时出错: %s", str(e))

            # 订阅图像话题
            image_sub = rospy.Subscriber(image_topic, Image, image_callback, queue_size=1, buff_size=2**24)
            rospy.loginfo(f"已订阅图像话题: {image_topic}，设置缓冲区大小为16MB")

            # 等待接收第一帧图像
            rospy.loginfo("等待接收图像...")
            wait_start = time.time()
            
            # 等待最多30秒
            while not self.frame_received and time.time() - wait_start < 30.0:
                # 每隔1秒检查一次
                rospy.sleep(1.0)
                rospy.loginfo("等待图像中...")

            if not self.frame_received:
                rospy.logerr("等待图像超时，请检查摄像头是否正常工作")
                return False

            rospy.loginfo("成功接收图像，开始旋转寻找目标...")

            # 创建显示窗口
            cv2.namedWindow("YOLO检测", cv2.WINDOW_NORMAL)
            cv2.resizeWindow("YOLO检测", 640, 480)

            # 确保cmd_vel_pub已正确初始化
            if not hasattr(self, 'cmd_vel_pub') or self.cmd_vel_pub is None:
                self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=10)
                rospy.sleep(0.5)  # 等待发布者初始化

            # 发送一个初始的停止命令，确保机器人从静止状态开始
            stop_cmd = Twist()
            self.cmd_vel_pub.publish(stop_cmd)
            rospy.sleep(0.5)

            # 不再使用TF获取机器人的初始姿态，改为完全使用时间积分方法
            rospy.loginfo("使用时间积分方法计算旋转角度")
            print(f"【旋转方法】: 使用时间积分计算旋转角度")
            
            # 记录起始时间
            start_time = rospy.Time.now()
            total_angle = 0.0  # 总旋转角度(通过时间积分计算)
            last_time = rospy.Time.now()
            
            # 最后一次检测角度
            last_detection_angle = -20
            # 已检测的角度列表（每一圈都分开记录）
            detected_angles = []
            # 记录已检测的圈数和角度组合
            detected_positions = set()
            current_rotation = 0  # 当前旋转的圈数
            current_angle_deg = 0  # 初始化当前角度为0度
            is_detecting = False  # 标志是否正在进行检测
            
            # 创建一个数组来跟踪旋转进度
            quadrants_visited = [False, False, False, False]  # 四个象限是否已访问
            
            print("\n" + "="*50)
            print(f"【开始旋转】: 正在旋转寻找{target_class_name}...")
            print("【旋转速度】: %.2f rad/s (约 %.1f 度/秒)" % (cmd_vel.angular.z, cmd_vel.angular.z * 180 / math.pi))
            print("【旋转计划】: 将旋转%d圈，每30°检测一次，共计%d次检测" % (target_rotations, 12 * target_rotations))
            print("="*50 + "\n")

            # 旋转直到检测到目标或完成目标圈数
            full_rotation_completed = False
            prev_angle_deg = 0
            
            # 在开始旋转前进行初始检测（0度位置）
            print("\n" + "="*50)
            print("【初始检测】: 在开始旋转前进行初始位置(0°)检测")
            print("="*50 + "\n")
            
            # 设置检测标志
            is_detecting = True
            
            # 确保当前帧有效
            if self.latest_frame is not None:
                # 复制当前帧，避免被回调函数修改
                current_frame = self.latest_frame.copy()

                # 执行YOLO检测
                results = model.predict(
                    source=current_frame,
                    conf=0.75,  # 置信度阈值
                    iou=0.45,
                    classes=target_class_ids,  # 使用动态设置的目标类别
                    verbose=False,
                    device='cpu',
                    workers=self.num_workers  # 使用多核CPU
                )
                
                # 检查是否检测到目标物体
                has_target = False
                best_target = None
                best_conf = 0
                detected_class_id = None
                
                # 遍历所有检测框
                for box in results[0].boxes:
                    cls = int(box.cls[0].item())
                    conf = box.conf[0].item()

                    # 只关注目标类别且置信度高于阈值
                    if cls in target_class_ids and conf > 0.75:
                        has_target = True
                        # 选择置信度最高的目标
                        if conf > best_conf:
                            best_conf = conf
                            best_target = box
                            detected_class_id = cls
                
                # 处理检测结果
                if has_target and best_target is not None:
                    # 获取目标边界框坐标
                    x1, y1, x2, y2 = map(int, best_target.xyxy[0].tolist())
                    
                    # 计算中心点和面积
                    target_center_x = int((x1 + x2) / 2)
                    target_center_y = int((y1 + y2) / 2)
                    detected_area = (x2 - x1) * (y2 - y1)
                    
                    # 获取检测到的类别名称
                    detected_class_name = class_names.get(detected_class_id, f"未知类别{detected_class_id}")
                    
                    # 打印检测结果
                    print("\n" + "="*50)
                    print(f"【检测结果】: 发现{detected_class_name}!")
                    print(f"【置信度】: {best_conf:.2f}")
                    print(f"【位置】: X={target_center_x}, Y={target_center_y}")
                    print(f"【大小】: {detected_area} 像素")
                    print("="*50 + "\n")
                    
                    # 设置检测到标志
                    banana_detected = True
                    
                    # 保存检测到的具体类别ID和名称，供后续追踪使用
                    self.detected_class_id = detected_class_id
                    self.target_class_ids = [detected_class_id]  # 设置为具体检测到的ID
                    
                    # 获取图像尺寸
                    height, width = current_frame.shape[:2]
                    
                    # 在图像上绘制检测结果
                    annotated_frame = results[0].plot()
                    
                    # 在显示的图像上添加更多信息
                    cv2.putText(annotated_frame, f"{detected_class_name}置信度: {best_conf:.2f}", (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(annotated_frame, f"{detected_class_name}位置: ({target_center_x}, {target_center_y})", (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(annotated_frame, f"{detected_class_name}面积: {detected_area} 像素", (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    
                    # 显示图像
                    cv2.imshow("YOLO检测", annotated_frame)
                    cv2.waitKey(1)

                    # 如果初始位置就检测到目标，则不需要旋转
                    if banana_detected:
                        print(f"【决定】: 初始位置已检测到{detected_class_name}，无需旋转")
                    else:
                        print(f"【初始检测结果】: 未检测到{target_class_name}，开始旋转搜索")
                        
                        # 显示初始图像
                        cv2.imshow("YOLO检测", annotated_frame)
                        cv2.waitKey(1)
                else:
                    print(f"【初始检测结果】: 未检测到{target_class_name}，开始旋转搜索")
                    
                    # 显示初始图像
                    if 'results' in locals() and len(results) > 0:
                        annotated_frame = results[0].plot()
                        cv2.imshow("YOLO检测", annotated_frame)
                        cv2.waitKey(1)
            else:
                print("【警告】: 初始位置无有效图像，跳过初始检测")
            
            # 重置检测标志和时间计算基准
            is_detecting = False
            last_time = rospy.Time.now()
            
            # 如果初始检测没有发现目标，开始旋转
            while (rospy.Time.now() - start_time) < timeout and not banana_detected and not full_rotation_completed:
                # 发布速度命令
                self.cmd_vel_pub.publish(cmd_vel)
                
                # 只有在实际旋转时才计算角度增量
                if not is_detecting:
                    # 使用时间积分计算旋转角度
                    current_time = rospy.Time.now()
                    dt = (current_time - last_time).to_sec()
                    angle_increment = cmd_vel.angular.z * dt
                    total_angle += angle_increment
                    last_time = current_time
                    
                    # 计算当前旋转的圈数和当前圈内的角度
                    current_rotation = int(total_angle / (2 * math.pi))
                    current_angle_deg = int((total_angle % (2 * math.pi)) * 180 / math.pi)
                    
                    # 更新象限访问情况
                    current_quadrant = current_angle_deg // 90
                    if 0 <= current_quadrant < 4:
                        quadrants_visited[current_quadrant] = True
                
                # 检查是否完成了目标旋转圈数
                if total_angle >= target_angle:
                    full_rotation_completed = True
                    print("\n" + "="*50)
                    print(f"【旋转完成】: 已旋转{target_rotations}圈，总角度: {total_angle * 180 / math.pi:.1f}°")
                    print("【象限状态】: " + " ".join([f"Q{i+1}:{'✓' if visited else '✗'}" for i, visited in enumerate(quadrants_visited)]))
                    print("="*50 + "\n")
                
                # 每隔15度打印一次旋转进度（不进行检测）
                if current_angle_deg % 15 == 0 and current_angle_deg != prev_angle_deg:
                    prev_angle_deg = current_angle_deg
                    elapsed_time = (rospy.Time.now() - start_time).to_sec()
                    estimated_time = (target_angle - total_angle) / cmd_vel.angular.z if cmd_vel.angular.z > 0 else 0
                    
                    # 只在非检测点打印简短进度
                    if current_angle_deg % 30 != 0:
                        print(f"【旋转进度】: {int(total_angle * 180 / math.pi)}°({total_angle / (2 * math.pi):.2f}圈) | "
                              f"已用时间: {elapsed_time:.1f}秒 | "
                              f"预计剩余: {estimated_time:.1f}秒")
                
                # 定义检测点角度列表
                detection_angles = [0, 60, 120, 180, 240, 300]
                
                # 检查是否到达检测点（添加角度容差）
                position_key = (current_rotation, current_angle_deg)
                
                # 检查当前角度是否接近任何检测点（允许±2度的误差）
                is_detection_angle = False
                for detection_angle in detection_angles:
                    if abs(current_angle_deg - detection_angle) <= 2:  # 允许±2度的误差
                        is_detection_angle = True
                        # 使用标准角度而不是实际角度，避免重复检测
                        current_angle_deg = detection_angle
                        position_key = (current_rotation, current_angle_deg)
                        break
                
                if is_detection_angle and position_key not in detected_positions:
                    
                    # 记录已检测的位置
                    detected_positions.add(position_key)
                    detected_angles.append(current_angle_deg)
                    
                    # 停止旋转
                    stop_cmd = Twist()
                    self.cmd_vel_pub.publish(stop_cmd)
                    
                    # 设置检测标志
                    is_detecting = True
                    
                    # 计算总检测次数
                    total_detections = len(detected_positions)
                    
                    # 打印详细的旋转信息
                    rospy.loginfo("已旋转 %d 度（第%d圈），停止进行检测...", current_angle_deg, current_rotation)
                    print(f"\n{'='*50}")
                    print(f"【检测点】: 第{current_rotation}圈的{current_angle_deg}°位置")
                    print(f"【总旋转】: {int(total_angle * 180 / math.pi)}° ({total_angle / (2 * math.pi):.2f}圈)")
                    print(f"【平均速度】: {cmd_vel.angular.z * 180 / math.pi:.1f}°/秒")
                    print(f"【已用时间】: {(rospy.Time.now() - start_time).to_sec():.1f}秒")
                    print(f"【检测进度】: {total_detections}/{max_detections} ({total_detections * 100.0 / max_detections:.1f}%)")
                    print(f"{'='*50}\n")
                    
                    # 确保当前帧有效
                    if self.latest_frame is None:
                        rospy.logwarn("当前帧为空，跳过检测")
                        print("【警告】: 当前帧为空，跳过检测")
                        # 恢复旋转
                        is_detecting = False  # 重置检测标志
                        last_time = rospy.Time.now()  # 重置时间计算基准
                        self.cmd_vel_pub.publish(cmd_vel)
                        continue
                        
                    # 复制当前帧，避免被回调函数修改
                    current_frame = self.latest_frame.copy()
                    
                    # 在该角度执行目标检测
                    print(f"【检测中】: 正在角度 {current_angle_deg}° 处检测{target_class_name}...")
                    
                    # 执行YOLO检测
                    results = model.predict(
                        source=current_frame,
                        conf=0.75,  # 置信度阈值
                        iou=0.45,
                        classes=target_class_ids,  # 使用动态设置的目标类别
                        verbose=False,
                        device='cpu',
                        workers=self.num_workers  # 使用多核CPU
                    )
                    
                    # 在图像上绘制检测结果
                    annotated_frame = results[0].plot()
                    
                    # 获取图像尺寸
                    height, width = annotated_frame.shape[:2]
                    center_x = width // 2
                    
                    # 将只读数组转换为可写数组
                    annotated_frame = np.array(annotated_frame, copy=True)
                    
                    # 绘制图像中心垂直线
                    cv2.line(annotated_frame, (center_x, 0), (center_x, height), (0, 0, 255), 1)
                    
                    # 添加旋转角度信息
                    angle_text = f"旋转角度: {current_angle_deg}° (第{current_rotation}圈)"
                    cv2.putText(annotated_frame, angle_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    
                    # 添加检测次数和象限信息
                    detection_count = len(detected_positions)
                    cv2.putText(annotated_frame, f"检测次数: {detection_count}/{max_detections}", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(annotated_frame, f"总旋转: {int(total_angle * 180 / math.pi)}°", (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    
                    # 显示检测窗口
                    cv2.imshow("YOLO检测", annotated_frame)
                    cv2.waitKey(1)
                    
                    # 检查是否检测到目标物体
                    has_target = False
                    best_target = None
                    best_conf = 0
                    detected_class_id = None
                    
                    # 遍历所有检测框
                    for box in results[0].boxes:
                        cls = int(box.cls[0].item())
                        conf = box.conf[0].item()
                        
                        # 只关注目标类别且置信度高于阈值
                        if cls in target_class_ids and conf > 0.75:
                            has_target = True
                            # 选择置信度最高的目标
                            if conf > best_conf:
                                best_conf = conf
                                best_target = box
                                detected_class_id = cls
                    
                    # 处理检测结果
                    if has_target and best_target is not None:
                        # 获取目标边界框坐标
                        x1, y1, x2, y2 = map(int, best_target.xyxy[0].tolist())
                        
                        # 计算中心点和面积
                        target_center_x = int((x1 + x2) / 2)
                        target_center_y = int((y1 + y2) / 2)
                        detected_area = (x2 - x1) * (y2 - y1)
                        
                        # 获取检测到的类别名称
                        detected_class_name = class_names.get(detected_class_id, f"未知类别{detected_class_id}")
                        
                        # 打印检测结果
                        print("\n" + "="*50)
                        print(f"【检测结果】: 发现{detected_class_name}!")
                        print(f"【置信度】: {best_conf:.2f}")
                        print(f"【位置】: X={target_center_x}, Y={target_center_y}")
                        print(f"【大小】: {detected_area} 像素")
                        print("="*50 + "\n")
                        
                        # 设置检测到标志
                        banana_detected = True
                        
                        # 保存检测到的具体类别ID和名称，供后续追踪使用
                        self.detected_class_id = detected_class_id
                        self.target_class_ids = [detected_class_id]  # 设置为具体检测到的ID
                        
                        # 在显示的图像上添加更多信息
                        cv2.putText(annotated_frame, f"{detected_class_name}置信度: {best_conf:.2f}", (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        cv2.putText(annotated_frame, f"{detected_class_name}位置: ({target_center_x}, {target_center_y})", (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        cv2.putText(annotated_frame, f"{detected_class_name}面积: {detected_area} 像素", (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        
                        # 更新显示
                        cv2.imshow("YOLO检测", annotated_frame)
                        cv2.waitKey(1)
                        
                        # 重置检测标志（虽然将要退出循环，但为了代码完整性）
                        is_detecting = False
                        
                        # 跳出旋转循环
                        break
                    else:
                        # 未检测到目标，打印消息并继续旋转
                        print(f"【检测结果】: 角度 {current_angle_deg}° 未检测到{target_class_name}")
                        print("【动作】: 继续旋转到下一个检测角度")
                        
                        # 等待1秒，让用户看清当前检测结果，然后继续旋转
                        rospy.sleep(1.0)
                        
                        # 重置检测标志和时间计算基准
                        is_detecting = False
                        last_time = rospy.Time.now()
                        
                        # 恢复旋转
                        self.cmd_vel_pub.publish(cmd_vel)
                
                # 打印每45度的旋转进度（避免重复打印）
                if current_angle_deg % 45 == 0 and current_angle_deg != prev_angle_deg:
                    print(f"【旋转进度】: 已旋转 {current_angle_deg}° ({(current_angle_deg / 360.0) * 100:.1f}%)")
                    prev_angle_deg = current_angle_deg
                
                # 控制循环速率
                rate.sleep()
            
            # 停止旋转
            stop_cmd = Twist()
            self.cmd_vel_pub.publish(stop_cmd)
            
            # 关闭所有OpenCV窗口
            cv2.destroyAllWindows()
            
            # 取消图像订阅
            image_sub.unregister()
            
            # 显示结果
            if banana_detected:
                result_msg = f"成功检测到{target_class_name}，旋转了 {total_angle * 180 / math.pi:.1f} 度（约 {total_angle / (2 * math.pi):.1f} 圈）"
                rospy.loginfo(result_msg)
                print("\n" + "="*50)
                print(f"【任务完成】: 成功检测到{target_class_name}!")
                print(f"【旋转角度】: {total_angle * 180 / math.pi:.1f}°（约 {total_angle / (2 * math.pi):.1f} 圈）")
                print(f"【检测次数】: {len(detected_positions)} 次")
                
                # 检查是否是在初始检测时就发现了目标
                if total_angle < 0.01:  # 几乎没有旋转
                    print(f"【检测位置】: 初始位置(0°)")
                else:
                    print(f"【检测位置】: 第{current_rotation}圈，{current_angle_deg}°")
                
                print("="*50 + "\n")
            else:
                completed_rotations = total_angle / (2 * math.pi)
                rospy.loginfo(f"完成 {completed_rotations:.1f} 圈旋转，未检测到{target_class_name}")
                print("\n" + "="*50)
                print(f"【任务结果】: 未检测到{target_class_name}")
                if full_rotation_completed:
                    print(f"【原因】: 已完成 {target_rotations} 圈旋转但未发现{target_class_name}")
                else:
                    print(f"【原因】: 旋转超时但未完成 {target_rotations} 圈")
                print(f"【旋转角度】: {total_angle * 180 / math.pi:.1f}°（约 {completed_rotations:.1f} 圈）")
                print(f"【检测次数】: {len(detected_positions)} 次")
                print("="*50 + "\n")

            return banana_detected
        
        except Exception as e:
            rospy.logerr(f"{target_class_name}检测时出错: %s" % str(e))
            rospy.logerr(traceback.format_exc())
            # 确保停止小车
            cmd_vel.angular.z = 0.0
            self.cmd_vel_pub.publish(cmd_vel)
            # 关闭所有OpenCV窗口
            try:
                cv2.destroyAllWindows()
            except:
                pass
            return False

    def detection_callback(self, msg):
        """YOLO检测结果回调函数"""
        try:
            # 解析检测结果
            detection_data = msg.data
            if "banana" in detection_data.lower():
                rospy.loginfo("YOLO检测到香蕉: %s", detection_data)
                self.banana_detected = True
        except Exception as e:
            rospy.logerr("处理检测结果时出错: %s" % str(e))

    def detect_traffic_light(self):
        """红绿灯检测功能，返回检测到的红绿灯状态"""
        rospy.loginfo("开始检测红绿灯...")
        print("\n" + "="*50)
        print("【任务】: 红绿灯检测")
        print("="*50 + "\n")

        # 获取当前导航点索引，用于确定是哪个红绿灯
        current_waypoint_index = self.current_waypoint
        # 根据导航点索引确定红绿灯编号
        if current_waypoint_index == 2:  # 索引2是红绿灯1
            traffic_light_number = 1
        elif current_waypoint_index == 3:  # 索引3是红绿灯2
            traffic_light_number = 2
        else:
            traffic_light_number = current_waypoint_index + 1  # 默认逻辑

        rospy.loginfo(f"当前检测的是红绿灯{traffic_light_number} (导航点索引: {current_waypoint_index})")
        print(f"【信息】: 当前检测的是红绿灯{traffic_light_number} (导航点索引: {current_waypoint_index})")

        try:
            # 加载红绿灯检测模型
            model_path = "/home/<USER>/ucar_ws/my_code/red.pt"
            if not os.path.exists(model_path):
                rospy.logerr("红绿灯检测模型不存在: %s", model_path)
                print(f"【错误】: 红绿灯检测模型不存在: {model_path}")
                return False

            rospy.loginfo("加载红绿灯检测模型: %s", model_path)
            print(f"【状态】: 加载红绿灯检测模型: {model_path}")
            model = YOLO(model_path)

            # 获取模型支持的类别
            class_names = model.names
            rospy.loginfo("模型支持的类别: %s", class_names)
            print(f"【信息】: 模型支持的类别: {class_names}")

            # 创建保存目录
            save_dir = "/home/<USER>/ucar_ws/my_code/pic"
            os.makedirs(save_dir, exist_ok=True)

            # 订阅相机图像话题
            self.bridge = CvBridge()
            self.latest_frame = None
            self.frame_received = False

            def image_callback(msg):
                try:
                    self.latest_frame = self.bridge.imgmsg_to_cv2(msg, "bgr8")
                    self.frame_received = True
                except Exception as e:
                    rospy.logerr("图像转换错误: %s", e)

            # 订阅图像话题
            image_sub = rospy.Subscriber('/usb_cam/image_raw', Image, image_callback)

            # 等待接收图像
            rospy.loginfo("等待接收图像...")
            print("【状态】: 等待接收图像...")

            timeout = 5  # 5秒超时
            start_time = time.time()

            while not self.frame_received and time.time() - start_time < timeout:
                rospy.sleep(0.1)

            if not self.frame_received:
                rospy.logerr("未能接收到图像，检测失败")
                print("【错误】: 未能接收到图像，检测失败")
                image_sub.unregister()
                return False

            # 复制当前帧，避免在处理过程中被回调函数修改
            current_frame = self.latest_frame.copy()

            # 执行检测
            rospy.loginfo("执行红绿灯检测...")
            print("【状态】: 执行红绿灯检测...")

            results = model.predict(
                source=current_frame,
                conf=0.25,
                iou=0.45,
                verbose=False,
                device='cpu',
                workers=num_workers  # 使用多核CPU
            )

            # 保存原始图像
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            img_filename = os.path.join(save_dir, f"traffic_light_original_{timestamp}.jpg")
            cv2.imwrite(img_filename, current_frame)
            rospy.loginfo("已保存原始图像到: %s", img_filename)
            print(f"【信息】: 已保存原始图像到: {img_filename}")

            # 处理检测结果
            detection_results = []
            is_green = False  # 默认不是绿灯
            rospy.loginfo("初始化绿灯状态: %s", is_green)
            print(f"【初始状态】: 绿灯状态 = {is_green}")

            if len(results) > 0 and len(results[0].boxes) > 0:
                # 获取检测结果
                boxes = results[0].boxes

                for i, box in enumerate(boxes):
                    # 获取类别和置信度
                    cls_id = int(box.cls[0].item())
                    conf = box.conf[0].item()

                    # 获取类别名称
                    cls_name = class_names[cls_id] if cls_id in class_names else f"未知类别{cls_id}"

                    # 获取边界框坐标
                    x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())

                    # 计算中心点和面积
                    center_x = (x1 + x2) // 2
                    center_y = (y1 + y2) // 2
                    area = (x2 - x1) * (y2 - y1)

                    # 添加到检测结果列表
                    detection_results.append({
                        'class_id': cls_id,
                        'class_name': cls_name,
                        'confidence': conf,
                        'box': (x1, y1, x2, y2),
                        'center': (center_x, center_y),
                        'area': area
                    })

                    # 打印检测结果
                    rospy.loginfo("检测到 %s: 置信度=%.2f, 位置=(%d,%d), 面积=%d",
                                 cls_name, conf, center_x, center_y, area)
                    print(f"【检测结果】: 检测到 {cls_name}")
                    print(f"【置信度】: {conf:.2f}")
                    print(f"【位置】: ({center_x},{center_y})")
                    print(f"【面积】: {area} 像素")
                    print("-" * 30)

                    # 检查是否是绿灯
                    if "green" in cls_name.lower() or "绿灯" in cls_name:
                        is_green = True
                        rospy.loginfo("检测到绿灯! 类别名称: %s, 置信度: %.2f", cls_name, conf)
                        print("\n" + "="*50)
                        print(f"【重要】: 检测到绿灯!")
                        print(f"【类别名称】: {cls_name}")
                        print(f"【置信度】: {conf:.2f}")
                        print("="*50 + "\n")

            # 显示检测结果摘要
            if detection_results:
                print("\n" + "="*50)
                print(f"【检测完成】: 共检测到 {len(detection_results)} 个目标")

                # 按类别统计
                class_counts = {}
                for result in detection_results:
                    cls_name = result['class_name']
                    if cls_name in class_counts:
                        class_counts[cls_name] += 1
                    else:
                        class_counts[cls_name] = 1

                for cls_name, count in class_counts.items():
                    print(f"【{cls_name}】: {count} 个")

                print("="*50 + "\n")
            else:
                print("\n" + "="*50)
                print("【检测完成】: 未检测到任何目标")
                print("="*50 + "\n")

            # 取消订阅图像话题
            image_sub.unregister()

            # 调试信息：打印检测到的所有类别和绿灯状态
            print("\n" + "="*50)
            print("【调试】: 检测到的所有类别:")
            for result in detection_results:
                print(f"  - {result['class_name']} (置信度: {result['confidence']:.2f})")
            print(f"【绿灯状态】: {'是' if is_green else '否'}")
            print(f"【当前红绿灯】: 红绿灯{traffic_light_number}")
            print("="*50 + "\n")

            # 根据红绿灯状态和编号设置下一个导航点
            if traffic_light_number == 1:  # 红绿灯1
                if is_green:
                    # 如果红绿灯1是绿灯，播放路口1音频，然后导航到路口1，并设置完成标志
                    rospy.loginfo("红绿灯1是绿灯，播放路口1音频并导航到路口1")
                    print("【决策】: 红绿灯1是绿灯，播放路口1音频并导航到路口1")

                    # 播放路口1音频
                    self.play_audio_file("/home/<USER>/ucar_ws/my_code/voice/路口1.wav", "路口1")

                    # 设置下一个导航点为路口1
                    self.next_waypoint = 4  # 索引4对应路口1 ([-15.400, -0.100,1.098,"",0,30])
                    self.navigation_complete_after_next = True  # 设置完成标志
                else:
                    # 如果红绿灯1不是绿灯（红灯），继续检测红绿灯2
                    self.traffic_light_1_is_red = True  # 设置红绿灯1是红灯的标志
                    self.next_waypoint = 3  # 索引3对应红绿灯2 ([-16.884, 0.516,-2.2,"detect_traffic_light",0,30])
                    rospy.loginfo("红绿灯1是红灯，将继续到红绿灯2位置")
                    print("【决策】: 红绿灯1是红灯，将继续到红绿灯2位置")
                    print("【状态】: 设置红绿灯1是红灯标志，红绿灯2将跳过检测")
            elif traffic_light_number == 2:  # 红绿灯2
                # 到达红绿灯2，不需要检测红绿灯状态，直接播放路口2音频并导航到路口2
                rospy.loginfo("到达红绿灯2位置，播放路口2音频并导航到路口2")
                print("【决策】: 到达红绿灯2位置，播放路口2音频并导航到路口2")
                print("【说明】: 红绿灯1是红灯时的路径，直接前往路口2")

                # 播放路口2音频
                self.play_audio_file("/home/<USER>/ucar_ws/my_code/voice/路口2.wav", "路口2")

                # 设置下一个导航点为路口2
                self.next_waypoint = 5  # 索引5对应路口2 ([-16.891, 1.113,0.550,"",0,30])
                self.navigation_complete_after_next = True  # 设置完成标志

            return True

        except Exception as e:
            rospy.logerr("红绿灯检测时出错: %s", str(e))
            rospy.logerr(traceback.format_exc())
            print(f"【错误】: 红绿灯检测失败: {str(e)}")
            return False

    # 添加更多的功能方法...

    def center_and_approach_banana(self):
        """将目标物体居中并前进到适当距离"""
        # 获取目标类别信息
        target_class_ids = self.target_class_ids if hasattr(self, 'target_class_ids') else [3]  # 默认检测香蕉
        target_class_name = self.target_class_name if hasattr(self, 'target_class_name') else "香蕉"
        
        # 如果只有一个目标类别ID，获取具体的检测到的ID
        detected_class_id = self.detected_class_id if hasattr(self, 'detected_class_id') else target_class_ids[0]
        
        rospy.loginfo(f"准备开始{target_class_name}居中和接近...")
        print(f"\n{'='*50}")
        print(f"【任务】: 目标居中和接近")
        print(f"【目标类别】: {target_class_ids} ({target_class_name})")
        if hasattr(self, 'detected_class_id'):
            print(f"【检测到的ID】: {self.detected_class_id}")
        print(f"{'='*50}\n")

        try:
            # 加载YOLO模型
            rospy.loginfo("加载YOLO模型...")
            print("正在加载目标检测模型，请稍候...")

            # 尝试多个可能的模型路径
            model_paths = [
                '/home/<USER>/ucar_ws/my_code/best.pt',  # 主要路径
                '/home/<USER>/ucar_ws/best.pt',          # 备用路径1
                '/home/<USER>/best.pt',                  # 备用路径2
            ]

            model_path = None
            for path in model_paths:
                if os.path.exists(path):
                    model_path = path
                    rospy.loginfo("找到模型文件: %s", model_path)
                    print(f"找到模型文件: {model_path}")
                    break

            # 检查模型文件是否存在
            if model_path is None:
                rospy.logerr("未找到任何模型文件，尝试过以下路径: %s", model_paths)
                print("错误: 未找到任何模型文件!")
                return False

            # 设置多线程环境变量
            if not hasattr(self, 'cpu_cores_setup_done'):
                num_cores = multiprocessing.cpu_count()
                self.num_workers = max(2, int(num_cores * 0.8))  # 使用80%的CPU核心
                os.environ["OMP_NUM_THREADS"] = str(self.num_workers)
                os.environ["MKL_NUM_THREADS"] = str(self.num_workers)
                self.cpu_cores_setup_done = True
                rospy.loginfo(f"多核设置完成: 检测到{num_cores}个核心，使用{self.num_workers}个核心进行处理")
                print(f"【多核设置】: 检测到{num_cores}个核心，使用{self.num_workers}个核心")
            else:
                rospy.loginfo(f"使用已设置的多核配置: {self.num_workers}个核心")

            # 加载模型
            rospy.loginfo("开始加载模型: %s", model_path)
            print(f"开始加载模型: {model_path}")
            
            # 加载YOLO模型
            model = YOLO(model_path)

            # 配置模型参数
            model.conf = 0.75  # 置信度阈值
            model.iou = 0.45   # IoU阈值

            # 获取模型支持的类别
            class_names = model.names
            rospy.loginfo("模型支持的类别: %s", class_names)
            print(f"模型支持的类别: {class_names}")

            # 设置要检测的目标类别（优先使用已检测到的具体ID）
            if hasattr(self, 'detected_class_id'):
                model.classes = [self.detected_class_id]  # 只检测已识别到的具体目标
                detected_class_name = class_names.get(self.detected_class_id, f"未知类别{self.detected_class_id}")
                rospy.loginfo(f"设置模型只检测{detected_class_name}(ID={self.detected_class_id})")
                print(f"【检测设置】: 只检测{detected_class_name}(ID={self.detected_class_id})")
            else:
                model.classes = target_class_ids  # 使用目标类别集合
                rospy.loginfo(f"设置模型检测类别: {target_class_ids} ({target_class_name})")
                print(f"【检测设置】: 检测类别={target_class_ids} ({target_class_name})")
            
            # 创建图像订阅者
            self.bridge = CvBridge()
            self.latest_frame = None
            self.frame_received = False

            # 订阅图像话题
            rospy.loginfo("订阅图像话题...")

            # 检查可用的图像话题
            available_topics = rospy.get_published_topics()
            image_topics = [topic for topic, topic_type in available_topics if topic_type == 'sensor_msgs/Image']

            # 优先使用校正后的图像话题
            if '/usb_cam/image_calibrated' in image_topics:
                image_topic = '/usb_cam/image_calibrated'
                rospy.loginfo("使用校正后的图像话题: %s", image_topic)
            elif '/usb_cam/image_raw' in image_topics:
                image_topic = '/usb_cam/image_raw'
                rospy.loginfo("使用原始图像话题: %s", image_topic)
            elif image_topics:
                image_topic = image_topics[0]
                rospy.loginfo("使用可用的图像话题: %s", image_topic)
            else:
                rospy.logerr("未找到任何图像话题，无法进行检测")
                return False

            # 定义图像回调函数
            def image_callback(msg):
                try:
                    # 将ROS图像转换为OpenCV格式
                    frame = self.bridge.imgmsg_to_cv2(msg, "bgr8")

                    # 检查图像是否有效
                    if frame is not None and frame.size > 0:
                        # 检查图像尺寸
                        height, width = frame.shape[:2]
                        if height > 0 and width > 0:
                            self.latest_frame = frame
                            self.frame_received = True
                            # 添加时间戳，用于检查图像是否更新
                            self.latest_frame_time = time.time()
                        else:
                            rospy.logwarn("收到无效图像，尺寸: %dx%d", width, height)
                    else:
                        rospy.logwarn("收到空图像")
                except Exception as e:
                    rospy.logerr("处理图像时出错: %s", str(e))

            # 订阅图像话题
            image_sub = rospy.Subscriber(image_topic, Image, image_callback, queue_size=1, buff_size=2**24)
            rospy.loginfo(f"已订阅图像话题: {image_topic}，设置缓冲区大小为16MB")

            # 等待接收第一帧图像
            rospy.loginfo("等待接收图像...")
            wait_start = time.time()
            
            # 等待最多30秒
            while not self.frame_received and time.time() - wait_start < 30.0:
                # 每隔1秒检查一次
                rospy.sleep(1.0)
                rospy.loginfo("等待图像中...")

            if not self.frame_received:
                rospy.logerr("等待图像超时，请检查摄像头是否正常工作")
                return False

            rospy.loginfo("成功接收图像，开始居中和接近目标...")

            # 创建显示窗口
            window_title = f"{target_class_name}检测与导航"
            cv2.namedWindow(window_title, cv2.WINDOW_NORMAL)
            cv2.resizeWindow(window_title, 640, 480)

            # 确保cmd_vel_pub已正确初始化
            if not hasattr(self, 'cmd_vel_pub') or self.cmd_vel_pub is None:
                self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=10)
                rospy.sleep(0.5)  # 等待发布者初始化

            # 发送一个初始的停止命令，确保机器人从静止状态开始
            stop_cmd = Twist()
            self.cmd_vel_pub.publish(stop_cmd)
            rospy.sleep(0.5)

            # 创建PID控制器
            # 水平位置PID（控制左右转向）
            pid_x = {
                'kp': 0.003,  # 增大比例系数，使角速度控制更敏感
                'ki': 0.0000,  # 积分系数
                'kd': 0.0005,  # 微分系数
                'error_sum': 0.0,  # 误差累积
                'last_error': 0.0,  # 上一次误差
                'last_time': time.time()  # 上一次更新时间
            }

            # 面积PID（控制前后移动）
            pid_area = {
                'kp': 0.0003,  # 增大比例系数，使线速度控制更敏感
                'ki': 0.00000,  # 积分系数
                'kd': 0.00001,  # 微分系数
                'error_sum': 0.0,  # 误差累积
                'last_error': 0.0,  # 上一次误差
                'last_time': time.time()  # 上一次更新时间
            }

            # 目标参数
            target_area = 50000  # 目标面积（像素），修改为50000
            area_tolerance = 5000  # 面积容差，设置为5000像素²
            center_tolerance = 30  # 中心点容差（像素），设置为30像素

            # 控制参数
            max_linear_speed = 0.3  # 最大线速度
            max_angular_speed = 0.5  # 最大角速度
            min_linear_speed = 0.05  # 最小线速度
            min_angular_speed = 0.05  # 最小角速度

            # 状态变量
            is_tracking = False  # 是否正在跟踪目标
            is_centered = False  # 目标是否已居中
            is_sized = False  # 目标是否已达到目标大小
            control_active = True  # PID控制是否激活
            horizontal_line_detected = False  # 是否检测到横线
            invert_direction = True  # 是否反转方向控制（如果相机是镜像的）

            # 设置超时时间
            timeout = rospy.Duration(180.0)  # 180秒超时
            start_time = rospy.Time.now()
            rate = rospy.Rate(10)  # 10Hz

            # 主循环
            print("\n" + "="*50)
            print(f"【开始导航】: 寻找并接近{target_class_name}...")
            print(f"【控制策略】: 1. 旋转寻找{target_class_name} 2. 居中{target_class_name} 3. 前进接近 4. 检测横线停止")
            print("="*50 + "\n")

            # 首先尝试检测目标
            banana_detected = False
            current_detection = None
            
            while (rospy.Time.now() - start_time) < timeout and not rospy.is_shutdown():
                # 确保当前帧有效
                if self.latest_frame is None:
                    rate.sleep()
                    continue
                    
                # 复制当前帧，避免被回调函数修改
                current_frame = self.latest_frame.copy()
                
                # 执行YOLO检测
                results = model.predict(
                    source=current_frame,
                    conf=0.75,  # 置信度阈值
                    iou=0.45,
                    classes=model.classes,  # 使用之前设置的类别列表
                    verbose=False,
                    device='cpu',
                    workers=self.num_workers  # 使用多核CPU
                )
                
                # 在图像上绘制检测结果
                annotated_frame = results[0].plot()
                
                # 获取图像尺寸
                height, width = annotated_frame.shape[:2]
                image_center_x = width // 2
                image_center_y = height // 2
                
                # 将只读数组转换为可写数组
                annotated_frame = np.array(annotated_frame, copy=True)
                
                # 绘制图像中心十字线
                cv2.line(annotated_frame, (image_center_x, 0), (image_center_x, height), (0, 0, 255), 1)
                cv2.line(annotated_frame, (0, image_center_y), (width, image_center_y), (0, 0, 255), 1)
                
                # 检查是否检测到目标物体
                has_target = False
                best_target = None
                best_conf = 0
                detected_obj_id = None
                
                # 遍历所有检测框
                for box in results[0].boxes:
                    cls = int(box.cls[0].item())
                    conf = box.conf[0].item()
                    
                    # 只关注目标类别且置信度高于阈值
                    if cls in model.classes and conf > 0.75:
                        has_target = True
                        # 选择置信度最高的目标
                        if conf > best_conf:
                            best_conf = conf
                            best_target = box
                            detected_obj_id = cls
                
                # 处理检测结果
                if has_target and best_target is not None:
                    # 获取目标边界框坐标
                    x1, y1, x2, y2 = map(int, best_target.xyxy[0].tolist())
                    
                    # 计算中心点和面积
                    target_center_x = int((x1 + x2) / 2)
                    target_center_y = int((y1 + y2) / 2)
                    target_area = (x2 - x1) * (y2 - y1)
                    
                    # 获取检测到的类别名称
                    detected_obj_name = class_names.get(detected_obj_id, f"未知类别{detected_obj_id}")
                    
                    # 设置检测到标志
                    banana_detected = True
                    
                    # 如果是第一次检测到目标，使用当前检测到的面积作为基准
                    if not hasattr(self, 'initial_detection_done') or not self.initial_detection_done:
                        # 保存检测到的初始面积作为目标面积
                        self.initial_target_area = 50000
                        
                        # 设置目标面积为一个合理的值，让机器人稍微前进到合适距离
                        # 通常我们希望物体在图像中稍大一些，所以目标面积设为当前的1.5倍
                        target_area_factor = 1.5
                        self.target_area_goal = 50000
                        
                        # print(f"\n【初始检测】: 当前面积={detected_area}像素²")
                        # print(f"【目标设置】: 设定目标面积为当前的{target_area_factor}倍 = {self.target_area_goal}像素²")
                        print(f"【控制说明】: 机器人将前进至物体面积达到目标值")
                        
                        self.initial_detection_done = True
                    
                    # 更新当前检测对象
                    current_detection = {
                        'center': (target_center_x, target_center_y),
                        'area': target_area,
                        'box': (x1, y1, x2, y2),
                        'confidence': best_conf,
                        'class_id': detected_obj_id,
                        'class_name': detected_obj_name
                    }
                    
                    # 设置跟踪标志
                    is_tracking = True
                    
                    # 在图像上绘制检测信息
                    cv2.putText(annotated_frame, f"{detected_obj_name}置信度: {best_conf:.2f}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(annotated_frame, f"{detected_obj_name}位置: ({target_center_x}, {target_center_y})", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(annotated_frame, f"{detected_obj_name}面积: {target_area} 像素", (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    
                    # 计算PID控制输出
                    if is_tracking and current_detection is not None:
                        # 获取当前时间
                        current_time = time.time()

                        # 获取目标中心点
                        target_center_x, target_center_y = current_detection['center']

                        # 获取目标面积
                        target_area_value = current_detection['area']

                        # 计算水平位置误差（正值表示目标在图像右侧，负值表示目标在图像左侧）
                        error_x = target_center_x - image_center_x

                        # 计算面积误差（正值表示目标太大，负值表示目标太小）
                        # 使用初始检测到的面积作为基准
                        target_area_baseline = self.initial_target_area if hasattr(self, 'initial_target_area') else target_area
                        error_area = target_area_value - target_area_baseline
                        
                        # 如果误差为0，设置一个非常小的值
                        if error_area == 0:
                            error_area = 0.001
                            
                        # 打印调试信息 (只在每10次循环打印一次，避免输出过多)
                        if not hasattr(self, 'debug_counter'):
                            self.debug_counter = 0
                        self.debug_counter = (self.debug_counter + 1) % 10
                        
                        if self.debug_counter == 0:
                            print(f"\n【面积计算】: 当前={target_area_value}, 目标={target_area_baseline}, 误差={error_area:+.1f}, 使用{'自动保存的初始值' if hasattr(self, 'initial_target_area') else '默认目标值'}")

                        # 计算时间差
                        dt_x = current_time - pid_x['last_time']
                        dt_area = current_time - pid_area['last_time']

                        # 防止dt过小
                        if dt_x < 0.01:
                            dt_x = 0.01
                        if dt_area < 0.01:
                            dt_area = 0.01

                        # 更新PID状态 - 水平位置
                        pid_x['error_sum'] += error_x * dt_x
                        error_diff_x = (error_x - pid_x['last_error']) / dt_x
                        pid_x['last_error'] = error_x
                        pid_x['last_time'] = current_time

                        # 更新PID状态 - 面积
                        pid_area['error_sum'] += error_area * dt_area
                        error_diff_area = (error_area - pid_area['last_error']) / dt_area
                        pid_area['last_error'] = error_area
                        pid_area['last_time'] = current_time

                        # 计算PID输出 - 水平位置（控制角速度）
                        # 检查是否需要反转角速度的符号（根据相机是否镜像）
                        # 正常逻辑：目标在右侧(error_x>0)，需要向右转(angular_z<0)
                        angular_z = -(pid_x['kp'] * error_x + pid_x['ki'] * pid_x['error_sum'] + pid_x['kd'] * error_diff_x)
                        
                        # 如果设置了方向反转，反转角速度符号
                        if invert_direction:
                            angular_z = -angular_z

                        # 计算PID输出 - 面积（控制线速度）
                        # 反转线速度的符号，解决前进后退方向相反的问题
                        linear_x = -(pid_area['kp'] * error_area + pid_area['ki'] * pid_area['error_sum'] + pid_area['kd'] * error_diff_area)

                        # 限制输出范围
                        angular_z = np.clip(angular_z, -max_angular_speed, max_angular_speed)
                        linear_x = np.clip(linear_x, -max_linear_speed, max_linear_speed)

                        # 设置最小速度（防止速度太小无法移动）
                        if 0 < abs(angular_z) < min_angular_speed:
                            angular_z = min_angular_speed if angular_z > 0 else -min_angular_speed
                        if 0 < abs(linear_x) < min_linear_speed:
                            linear_x = min_linear_speed if linear_x > 0 else -min_linear_speed

                        # 检查目标是否已居中
                        is_centered = abs(error_x) < center_tolerance

                        # 检查目标是否已达到目标大小
                        is_sized = abs(error_area) < area_tolerance

                        # 如果目标已居中，停止水平控制
                        if is_centered:
                            angular_z = 0.0
                            cv2.putText(annotated_frame, "目标已居中", (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        else:
                            cv2.putText(annotated_frame, "正在居中目标...", (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

                        # 如果目标已达到目标大小，停止前后控制
                        if is_sized:
                            linear_x = 0.0
                            cv2.putText(annotated_frame, "已达到目标距离", (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        else:
                            cv2.putText(annotated_frame, "正在调整距离...", (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

                        # 检测图像下半部分是否有横线
                        # 截取图像下半部分进行处理
                        crop_ratio = 0.7  # 截取下30%
                        crop_start_y = int(height * crop_ratio)
                        cropped_frame = current_frame[crop_start_y:, :]
                        
                        # 转换为灰度图
                        gray = cv2.cvtColor(cropped_frame, cv2.COLOR_BGR2GRAY)
                        
                        # 应用高斯模糊
                        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
                        
                        # 应用Canny边缘检测
                        edges = cv2.Canny(blurred, 50, 150)
                        
                        # 使用霍夫直线变换检测直线
                        min_line_length = int(width * 0.6)  # 最小线长为图像宽度的60%
                        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                                              minLineLength=min_line_length, maxLineGap=20)
                        
                        # 在原图上绘制裁剪区域
                        cv2.line(annotated_frame, (0, crop_start_y), (width, crop_start_y), (255, 0, 0), 2)
                        
                        # 检查是否检测到横线
                        if lines is not None:
                            for line in lines:
                                x1_line, y1_line, x2_line, y2_line = line[0]
                                # 计算直线角度
                                angle = np.abs(np.arctan2(y2_line - y1_line, x2_line - x1_line) * 180 / np.pi)
                                
                                # 检查是否为横向直线（角度接近0度或180度）
                                if angle < 15 or angle > 165:
                                    line_length = np.sqrt((x2_line - x1_line)**2 + (y2_line - y1_line)**2)
                                    # 如果横向直线足够长，认为检测到停车线
                                    if line_length > min_line_length:
                                        horizontal_line_detected = True
                                        # 在裁剪图像上绘制检测到的线
                                        cv2.line(annotated_frame, 
                                                (x1_line, y1_line + crop_start_y), 
                                                (x2_line, y2_line + crop_start_y), 
                                                (0, 0, 255), 2)
                                        cv2.putText(annotated_frame, "检测到横线!", (10, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                                        break
                        
                        # 如果检测到横线，停止移动
                        if horizontal_line_detected:
                            linear_x = 0.0
                            angular_z = 0.0
                            cv2.putText(annotated_frame, "已停止 - 检测到横线", (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                        
                        # 发送控制命令
                        if control_active:
                            cmd_vel = Twist()
                            cmd_vel.linear.x = linear_x
                            cmd_vel.angular.z = angular_z
                            self.cmd_vel_pub.publish(cmd_vel)
                            
                            # 在图像上显示控制信息
                            cv2.putText(annotated_frame, f"角速度: {angular_z:.4f} rad/s", (width - 250, height - 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                            cv2.putText(annotated_frame, f"线速度: {linear_x:.4f} m/s", (width - 250, height - 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                            cv2.putText(annotated_frame, f"位置误差: {error_x:+.1f}px", (width - 250, height - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                            
                            # 绘制目标位置与中心的连线
                            cv2.line(annotated_frame, (image_center_x, image_center_y), (target_center_x, target_center_y), (0, 255, 255), 2)
                            
                            # 显示PID参数
                            cv2.putText(annotated_frame, f"PID-X: kp={pid_x['kp']}", (10, height - 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
                            cv2.putText(annotated_frame, f"方向反转: {'开启' if invert_direction else '关闭'}", (10, height - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
                            
                            # 打印控制信息
                            print(f"\r位置误差: {error_x:+.1f}px, 面积误差: {error_area:+.1f}px², 角速度: {angular_z:.4f}, 线速度: {linear_x:.4f}", end="")
                    else:
                        # 未检测到香蕉，旋转寻找
                        if not banana_detected:
                            cmd_vel = Twist()
                            cmd_vel.angular.z = 0.3  # 慢速旋转
                            self.cmd_vel_pub.publish(cmd_vel)
                            cv2.putText(annotated_frame, f"未检测到{target_class_name}，正在旋转寻找...", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                        else:
                            # 之前检测到过目标，但现在丢失了，停止移动
                            cmd_vel = Twist()
                            self.cmd_vel_pub.publish(cmd_vel)
                            cv2.putText(annotated_frame, f"{target_class_name}丢失，已停止", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                            is_tracking = False
                            current_detection = None
                
                # 显示图像
                cv2.imshow(window_title, annotated_frame)
                key = cv2.waitKey(1) & 0xFF
                
                # 如果按下ESC键，退出循环
                if key == 27:
                    break
                    
                # 如果检测到横线，等待3秒后退出
                if horizontal_line_detected:
                    print("\n" + "="*50)
                    print("【导航完成】: 检测到横线，已停止")
                    print("="*50 + "\n")
                    rospy.sleep(3.0)
                    break
                
                # 检查是否满足前进条件：目标已居中且面积误差在容差范围内
                if is_tracking and current_detection is not None:
                    # 计算误差
                    position_error = abs(error_x)
                    area_error = abs(error_area)
                    
                    # 获取目标物体信息
                    target_obj_name = current_detection.get('class_name', target_class_name)
                    
                    # 打印详细的状态信息
                    print(f"\r位置误差: {error_x:+.1f}px (阈值: ±30px), 面积误差: {error_area:+.1f}px² (阈值: ±5000px²), 角速度: {angular_z:.4f}, 线速度: {linear_x:.4f}", end="")
                    
                    # 每5秒打印一次完整状态
                    current_time = time.time()
                    if not hasattr(self, 'last_status_time') or current_time - self.last_status_time > 5.0:
                        self.last_status_time = current_time
                        print("\n" + "="*50)
                        print(f"【状态报告】: 时间 {current_time:.1f}")
                        print(f"【跟踪目标】: {target_obj_name}")
                        print(f"【位置误差】: {error_x:+.1f}px (阈值: ±30px)")
                        print(f"【面积误差】: {error_area:+.1f}px² (阈值: ±5000px²)")
                        print(f"【当前面积】: {target_area_value} 像素²")
                        print(f"【目标面积】: {target_area_baseline} 像素²")
                        print(f"【是否居中】: {'是' if position_error <= 30 else '否'}")
                        print(f"【面积合适】: {'是' if area_error <= 5000 else '否'}")
                        print(f"【控制输出】: 角速度={angular_z:.4f}, 线速度={linear_x:.4f}")
                        if current_detection and 'center' in current_detection:
                            print(f"【检测结果】: {target_obj_name}位置={current_detection['center']}, 面积={current_detection['area']}")
                        print("="*50)
                    
                    # 检查误差是否在指定范围内（位置误差≤30像素且面积误差≤5000像素²）
                    if position_error <= 30 and area_error <= 5000:
                        print("\n" + "="*50)
                        print("【状态更新】: 目标已满足条件，开始前进10cm")
                        print(f"【位置误差】: {error_x:+.1f}px (阈值: ±30px)")
                        print(f"【面积误差】: {error_area:+.1f}px² (阈值: ±5000px²)")
                        print("="*50)
                        
                        # 显示状态
                        cv2.putText(annotated_frame, "目标已满足条件，准备前进", (10, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        cv2.imshow(window_title, annotated_frame)
                        cv2.waitKey(1)
                        
                        # 调用前进函数
                        self.move_forward_fixed_distance(0.1, 0.1)
                        
                        # 设置标志，表示已完成任务
                        horizontal_line_detected = True  # 使用横线检测标志来结束循环
                        break
                
                # 控制循环速率
                rate.sleep()
            
            # 停止移动
            stop_cmd = Twist()
            self.cmd_vel_pub.publish(stop_cmd)
            
            # 关闭所有OpenCV窗口
            cv2.destroyAllWindows()
            
            # 取消图像订阅
            image_sub.unregister()
            
            # 显示结果
            if horizontal_line_detected:
                rospy.loginfo(f"成功导航到{target_class_name}并检测到横线")
                return True
            elif banana_detected:
                rospy.loginfo(f"成功检测到{target_class_name}，但未检测到横线")
                return True
            else:
                rospy.loginfo(f"未能检测到{target_class_name}")
                return False
                
        except Exception as e:
            rospy.logerr(f"{target_class_name}居中和接近过程中出错: %s" % str(e))
            rospy.logerr(traceback.format_exc())
            # 确保停止小车
            cmd_vel = Twist()
            cmd_vel.linear.x = 0.0
            cmd_vel.angular.z = 0.0
            self.cmd_vel_pub.publish(cmd_vel)
            # 关闭所有OpenCV窗口
            try:
                cv2.destroyAllWindows()
            except:
                pass
            return False

    def move_forward_fixed_distance(self, distance=0.1, speed=0.1):
        """控制机器人前进固定距离
        
        参数:
            distance: 前进距离，单位米
            speed: 前进速度，单位米/秒
        """
        rospy.loginfo(f"开始前进{distance}米，速度{speed}米/秒")
        print(f"\n{'='*50}")
        print(f"【开始前进】: 距离={distance}m, 速度={speed}m/s")
        print(f"{'='*50}\n")
        
        # 创建速度消息
        cmd_vel = Twist()
        cmd_vel.linear.x = speed
        
        # 计算需要的时间
        forward_time = distance / speed
        
        # 发送速度命令
        self.cmd_vel_pub.publish(cmd_vel)
        
        # 等待指定时间
        rospy.sleep(forward_time)
        
        # 停止移动
        cmd_vel.linear.x = 0.0
        self.cmd_vel_pub.publish(cmd_vel)
        
        rospy.loginfo(f"已前进{distance}米")
        print(f"\n{'='*50}")
        print(f"【前进完成】: 已前进{distance}米")
        print(f"{'='*50}\n")
        
        return True

    def scan_qrcode_and_set_target(self):
        """
        扫描二维码并根据内容设置检测目标类别
        
        二维码内容与目标类别对应关系:
        - "Vegetable": 检测蔬菜类 (ID=0,1,2，辣椒、西红柿、土豆)
        - "Fruit": 检测水果类 (ID=3,4,5，香蕉、苹果、西瓜)
        - "Dessert": 检测甜点类 (ID=6,7,8，可乐、蛋糕、牛奶)
        - 其他内容: 默认检测香蕉 (ID=3)
        
        返回:
            list: 目标类别ID列表
        """
        rospy.loginfo("开始扫描二维码以确定检测目标...")
        print("\n" + "="*50)
        print("【任务】: 扫描二维码确定检测目标")
        print("【说明】: 扫描内容决定目标类别")
        print("   - Vegetable → 蔬菜类 (ID=0,1,2)")
        print("   - Fruit → 水果类 (ID=3,4,5)")
        print("   - Dessert → 甜点类 (ID=6,7,8)")
        print("   - 其他内容 → 默认检测香蕉 (ID=3)")
        print("="*50 + "\n")

        # 设置扫描状态
        self.is_scanning_qr = True
        self.qr_result = None

        # 订阅相机图像话题
        self.image_sub = rospy.Subscriber('/usb_cam/image_raw', Image, self.image_callback)

        # 等待扫描结果，最多等待10秒
        start_time = rospy.Time.now()
        timeout = rospy.Duration(10.0)  # 10秒超时
        rate = rospy.Rate(3)  # 3Hz

        while self.is_scanning_qr and (rospy.Time.now() - start_time) < timeout:
            if self.qr_result:
                rospy.loginfo("扫描到二维码: %s" % self.qr_result)
                break
            rate.sleep()

        # 取消订阅
        if self.image_sub:
            self.image_sub.unregister()
            self.image_sub = None

        # 根据二维码内容设置检测目标
        qr_content = self.qr_result if self.qr_result else ""
        
        # 确定检测目标类别
        if qr_content == "Vegetable":
            # 检测蔬菜类（ID为0、1、2，即辣椒、西红柿、土豆）
            target_class_ids = [0, 1, 2]
            target_class_name = "蔬菜"
        elif qr_content == "Fruit":
            # 检测水果类（ID为3、4、5，即香蕉、苹果、西瓜）
            target_class_ids = [3, 4, 5]
            target_class_name = "水果"
        elif qr_content == "Dessert":
            # 检测甜点类（ID为6、7、8，即可乐、蛋糕、牛奶）
            target_class_ids = [6, 7, 8]
            target_class_name = "甜点"
        else:
            # 如果二维码内容不匹配，默认检测香蕉
            target_class_ids = [3]  # 默认检测香蕉
            target_class_name = "香蕉"
            rospy.logwarn("未识别的二维码内容 '%s'，默认检测香蕉", qr_content)
            print(f"未识别的二维码内容 '{qr_content}'，默认检测香蕉")
            
        # 保存检测目标信息
        self.target_class_ids = target_class_ids
        self.target_class_name = target_class_name
        
        rospy.loginfo(f"设置检测目标为: {target_class_name} (类别ID: {target_class_ids})")
        print(f"\n{'='*50}")
        print(f"【检测目标】: {target_class_name}")
        print(f"【类别ID】: {target_class_ids}")
        print(f"{'='*50}\n")
        
        # 重置扫描状态
        self.is_scanning_qr = False
        
        return target_class_ids

    def xushijiehe(self):
        """
        Communicates with the backend server to send QR content and get result ID
        
        1. Sends the QR content to the server
        2. Retrieves the result ID from the server, polling every 10 seconds until success
        3. Verifies if the ID is valid (between 0-8)
        
        Returns:
            bool: True if successful, False otherwise
        """
        rospy.loginfo("开始执行虚实结合任务...")
        print("\n" + "="*50)
        print("【任务】: 虚实结合")
        print("【说明】: 与后端服务器通信，发送QR码内容并获取结果")
        print("="*50 + "\n")
        
        try:
            # Get the QR content (use the previously scanned value or default to "Fruit")
            qr_content = self.qr_result if hasattr(self, 'qr_result') and self.qr_result else "Fruit"
            
            # API endpoints
            post_url = "http://192.168.234.104:5000/api/detect/"
            get_url = "http://192.168.234.104:5000/api/result/"
            
            # Prepare data to send
            post_data = {
                "qr_content": qr_content
            }
            
            # Print request info
            print(f"【请求】: POST {post_url}")
            print(f"【数据】: {post_data}")
            
            # Send POST request to the server
            rospy.loginfo(f"向后端发送QR内容: {qr_content}")
            post_response = requests.post(post_url, json=post_data, timeout=10)
            
            # Check if POST request was successful
            if post_response.status_code == 200:
                rospy.loginfo("POST请求成功")
                print(f"【状态】: POST请求成功 (状态码: {post_response.status_code})")
                print(f"【响应】: {post_response.text}")
            else:
                rospy.logerr(f"POST请求失败，状态码: {post_response.status_code}")
                print(f"【错误】: POST请求失败 (状态码: {post_response.status_code})")
                print(f"【响应】: {post_response.text}")
                return False
            
            # Wait a moment for the server to process
            rospy.sleep(2.0)
            
            # Set up polling for result
            max_attempts = 30  # Maximum number of attempts (5 minutes with 10s interval)
            attempt = 0
            success = False
            result_id = None
            
            print(f"【状态】: 开始轮询结果，每10秒检查一次，最多尝试{max_attempts}次")
            
            while attempt < max_attempts and not success:
                attempt += 1
                
                # Print request info for GET
                print(f"【请求】: GET {get_url} (尝试 {attempt}/{max_attempts})")
                
                try:
                    # Send GET request to get the result
                    rospy.loginfo(f"从后端获取结果... (尝试 {attempt}/{max_attempts})")
                    get_response = requests.get(get_url, timeout=10)
                    
                    # Check if GET request was successful
                    if get_response.status_code == 200:
                        # Parse the response
                        result_data = get_response.json()
                        
                        # Check if message is "success"
                        if 'message' in result_data and result_data['message'] == "success":
                            # Extract the ID from the response
                            if 'id' in result_data:
                                result_id = result_data['id']
                                rospy.loginfo(f"获取到结果ID: {result_id}")
                                print(f"【状态】: GET请求成功 (状态码: {get_response.status_code})")
                                print(f"【消息】: {result_data['message']}")
                                print(f"【结果】: ID = {result_id}")
                                success = True
                                break
                            else:
                                rospy.logwarn("响应中包含'success'消息，但未找到ID字段")
                                print(f"【警告】: 响应中包含'success'消息，但未找到ID字段")
                                print(f"【响应数据】: {result_data}")
                        else:
                            # Message is not "success", continue polling
                            message = result_data.get('message', '无消息')
                            rospy.loginfo(f"后端尚未处理完成，消息: {message}")
                            print(f"【状态】: 后端尚未处理完成，消息: {message}")
                            print(f"【等待】: 10秒后重试...")
                    else:
                        rospy.logwarn(f"GET请求失败，状态码: {get_response.status_code}")
                        print(f"【警告】: GET请求失败 (状态码: {get_response.status_code})")
                        print(f"【响应】: {get_response.text}")
                        print(f"【等待】: 10秒后重试...")
                
                except requests.exceptions.RequestException as e:
                    rospy.logwarn(f"网络请求错误: {str(e)}")
                    print(f"【警告】: 网络请求失败 - {str(e)}")
                    print(f"【等待】: 10秒后重试...")
                
                # Wait 10 seconds before next attempt if not successful
                if not success:
                    rospy.sleep(10.0)
            
            # Check final result
            if success and result_id is not None:
                # Verify if the ID is valid (between 0-8)
                if 0 <= result_id <= 8:
                    rospy.loginfo(f"结果ID有效: {result_id} (在0-8范围内)")
                    print(f"【验证】: ID有效 (在0-8范围内)")
                    print(f"【任务】: 成功完成虚实结合任务")
                    return True
                else:
                    rospy.logwarn(f"结果ID无效: {result_id} (不在0-8范围内)")
                    print(f"【验证】: ID无效 (不在0-8范围内)")
                    return False
            else:
                rospy.logerr(f"在{max_attempts}次尝试后未能获取成功结果")
                print(f"【错误】: 在{max_attempts}次尝试后未能获取成功结果")
                return False
                
        except Exception as e:
            rospy.logerr(f"虚实结合任务执行错误: {str(e)}")
            rospy.logerr(traceback.format_exc())
            print(f"【错误】: 执行失败 - {str(e)}")
            return False

def main():
    try:
        # 创建导航对象
        navigator = WaypointNavigation()

        # 检查move_base服务器是否可用
        if navigator.client is None:
            rospy.logwarn("move_base客户端未初始化，但将继续执行程序")
            # 不启动任何其他程序，直接继续
        elif not navigator.client.wait_for_server(rospy.Duration(3.0)):
            rospy.logwarn("move_base服务器不可用，但将继续执行程序")
            # 不启动任何其他程序，直接继续
        else:
            rospy.loginfo("move_base服务器已准备就绪")

        # 设置初始位置（使用当前实际位置）
        navigator.set_initial_pose(1.276, 0.462, 2.982)

        # 等待一下，确保初始位置已经生效
        rospy.sleep(2.0)

        # 不需要等待用户确认，直接开始导航
        rospy.loginfo("自动开始导航...")

        # 导航所有点
        navigator.navigate_all_waypoints()

        rospy.loginfo("导航任务完成")

    except rospy.ROSInterruptException:
        rospy.loginfo("导航被中断")
    except Exception as e:
        rospy.logerr("发生错误: %s" % str(e))

if __name__ == '__main__':
    main()
