#!/bin/bash
# 运行键盘控制小车程序

# 确保ROS环境已经设置
source /opt/ros/noetic/setup.bash
source /home/<USER>/ucar_ws/devel/setup.bash

# 设置ROS网络配置
export ROS_MASTER_URI=http://localhost:11311
export ROS_IP=**************
export ROS_HOSTNAME=**************

# 检查ROS是否在运行
if ! pgrep -x "rosmaster" > /dev/null; then
    echo "ROS核心未运行，启动roscore..."
    roscore &
    ROSCORE_PID=$!
    sleep 3
fi

# 检查底盘控制器是否在运行
echo "检查底盘控制器状态..."
if ! rostopic list | grep -q "/cmd_vel"; then
    echo "启动底盘控制器..."
    roslaunch ucar_controller base_driver.launch &
    CONTROLLER_PID=$!
    sleep 3
else
    echo "底盘控制器已在运行"
fi

# 激活Python 3.8虚拟环境
echo "激活Python 3.8虚拟环境..."
source ~/miniconda3/bin/activate py38

# 运行键盘控制程序
echo "运行键盘控制小车程序..."
python /home/<USER>/ucar_ws/my_code/control_car_keyboard.py

# 如果我们启动了底盘控制器，在程序退出后关闭它
if [ -n "$CONTROLLER_PID" ]; then
    echo "关闭底盘控制器..."
    kill $CONTROLLER_PID
fi

echo "程序已退出"
