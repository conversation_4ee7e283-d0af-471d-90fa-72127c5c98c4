#!/usr/bin/env python3
"""
更新RViz配置文件的焦点位置
"""

import os
import shutil

def update_rviz_config():
    """更新RViz配置文件的焦点位置"""
    
    rviz_file = "/home/<USER>/maps/wmd.rviz"
    backup_file = "/home/<USER>/maps/wmd.rviz.backup"
    
    try:
        # 备份原文件
        if os.path.exists(rviz_file):
            shutil.copy2(rviz_file, backup_file)
            print(f"✅ 已备份原文件到: {backup_file}")
        
        # 读取文件内容
        with open(rviz_file, 'r') as f:
            content = f.read()
        
        # 替换焦点位置
        # 从 X: -0.049, Y: 0.109 改为 X: 0.065, Y: 0.071
        content = content.replace('X: -0.049', 'X: 0.065')
        content = content.replace('Y: 0.109', 'Y: 0.071')
        
        # 写回文件
        with open(rviz_file, 'w') as f:
            f.write(content)
        
        print("✅ RViz配置文件已更新")
        print("📍 新的焦点位置: (0.065, 0.071, 0.0)")
        
        # 验证修改
        print("\n验证修改结果:")
        with open(rviz_file, 'r') as f:
            lines = f.readlines()
            
        for i, line in enumerate(lines):
            if "Focal Point:" in line:
                print(f"第{i+1}行: {line.strip()}")
                for j in range(1, 4):
                    if i+j < len(lines):
                        print(f"第{i+j+1}行: {lines[i+j].strip()}")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 更新RViz配置文件时出错: {e}")
        return False

if __name__ == '__main__':
    update_rviz_config()
