#!/bin/bash

# 交互式角度数据采集脚本

echo "🎯 车辆角度数据采集工具"
echo "=========================="
echo

# 设置ROS环境
source /home/<USER>/ucar_ws/devel/setup.bash
export ROS_MASTER_URI=http://localhost:11311
export ROS_IP=**************
export ROS_HOSTNAME=**************

# 创建结果文件
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULT_FILE="angle_data_${TIMESTAMP}.txt"

echo "车辆角度数据采集结果" > $RESULT_FILE
echo "采集时间: $(date)" >> $RESULT_FILE
echo "=========================" >> $RESULT_FILE
echo >> $RESULT_FILE

# 定义要采集的角度
declare -a angles=("0度(初始位置)" "90度(右转)" "180度(掉头)" "270度(左转)")
declare -a angle_values=("0" "90" "180" "270")

for i in "${!angles[@]}"; do
    angle_name="${angles[$i]}"
    angle_value="${angle_values[$i]}"
    
    echo "📍 请将车辆调整到: $angle_name"
    echo "   确保车辆完全静止后按回车键继续..."
    read -p "   " dummy
    
    echo "🔄 正在采集 $angle_name 的数据..."
    
    # 采集TF数据
    tf_output=$(timeout 5 rosrun tf tf_echo map base_link 2>/dev/null | head -20)
    
    if [ $? -eq 0 ] && [ ! -z "$tf_output" ]; then
        echo "✅ $angle_name 数据采集成功"
        
        # 保存到文件
        echo "【$angle_name】" >> $RESULT_FILE
        echo "采集时间: $(date)" >> $RESULT_FILE
        echo "$tf_output" >> $RESULT_FILE
        echo >> $RESULT_FILE
        
        # 提取并显示关键信息
        translation=$(echo "$tf_output" | grep -A 1 "Translation:" | tail -1)
        rotation=$(echo "$tf_output" | grep -A 1 "Rotation:" | tail -1)
        rpy_degree=$(echo "$tf_output" | grep "RPY (degree)" | tail -1)
        
        echo "   位置: $translation"
        echo "   角度: $rpy_degree"
        
    else
        echo "❌ $angle_name 数据采集失败"
        echo "【$angle_name】" >> $RESULT_FILE
        echo "采集时间: $(date)" >> $RESULT_FILE
        echo "数据采集失败" >> $RESULT_FILE
        echo >> $RESULT_FILE
    fi
    
    echo
done

echo "🎉 所有角度数据采集完成！"
echo "📁 结果已保存到: $RESULT_FILE"
echo

# 显示摘要
echo "📊 采集结果摘要:"
echo "=================="
cat $RESULT_FILE | grep -E "【|Translation|RPY \(degree\)" | while read line; do
    if [[ $line == *"【"* ]]; then
        echo "$line"
    elif [[ $line == *"Translation"* ]]; then
        echo "  位置: $line"
    elif [[ $line == *"RPY (degree)"* ]]; then
        echo "  角度: $line"
        echo
    fi
done

echo "💡 提示:"
echo "   - 数据文件: $RESULT_FILE"
echo "   - 可以用这些数据更新导航脚本中的角度设置"
echo "   - 四元数数据可用于精确的角度控制"
