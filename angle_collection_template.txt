车辆角度数据采集记录表
========================

采集日期: _______________
采集人员: _______________

使用命令: ./get_current_angle.sh
或者: source /home/<USER>/ucar_ws/devel/setup.bash && export ROS_MASTER_URI=http://localhost:11311 && export ROS_IP=************** && export ROS_HOSTNAME=************** && timeout 10 rosrun tf tf_echo map base_link

采集步骤:
1. 将车辆调整到目标角度
2. 运行上述命令获取TF数据
3. 记录Translation和Rotation数值

=== 数据记录 ===

【初始位置 - 0度】
时间: _______________
Translation:
  x: _______________
  y: _______________
  z: _______________
Rotation (四元数):
  x: _______________
  y: _______________
  z: _______________
  w: _______________

【右转90度】
时间: _______________
Translation:
  x: _______________
  y: _______________
  z: _______________
Rotation (四元数):
  x: _______________
  y: _______________
  z: _______________
  w: _______________

【掉头180度】
时间: _______________
Translation:
  x: _______________
  y: _______________
  z: _______________
Rotation (四元数):
  x: _______________
  y: _______________
  z: _______________
  w: _______________

【左转270度】
时间: _______________
Translation:
  x: _______________
  y: _______________
  z: _______________
Rotation (四元数):
  x: _______________
  y: _______________
  z: _______________
  w: _______________

=== 备注 ===
- 四元数转欧拉角公式: yaw = atan2(2*(w*z + x*y), 1-2*(y*y + z*z))
- 角度转换: 弧度 * 180 / π = 度
- 确保每次采集时车辆静止
- 记录时保留至少3位小数
