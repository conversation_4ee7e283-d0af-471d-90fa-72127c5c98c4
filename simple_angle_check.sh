#!/bin/bash

# 简单的角度检查脚本

echo "🎯 当前车辆角度检查"
echo "=================="

# 设置ROS环境
source /home/<USER>/ucar_ws/devel/setup.bash
export ROS_MASTER_URI=http://localhost:11311
export ROS_IP=**************
export ROS_HOSTNAME=**************

echo "🔄 获取TF数据中..."
echo

# 直接运行命令并显示结果
timeout 5 rosrun tf tf_echo map base_link | head -5

echo
echo "✅ 完成"
echo
echo "💡 说明:"
echo "   Translation [x, y, z] = 位置坐标"
echo "   Quaternion [x, y, z, w] = 四元数朝向"
echo "   RPY (degree) [roll, pitch, yaw] = 欧拉角(度)"
echo "   其中 yaw 是车辆的朝向角度"
