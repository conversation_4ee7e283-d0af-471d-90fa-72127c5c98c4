#!/usr/bin/env python3

import rospy
from geometry_msgs.msg import Twist
import sys
import tty
import termios
import select

class FastRobotControl:
    def __init__(self):
        rospy.init_node('fast_robot_control', anonymous=True)
        self.pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
        
        # 速度设置
        self.linear_speed = 0.3
        self.angular_speed = 0.8
        
        # 保存终端设置
        self.settings = termios.tcgetattr(sys.stdin)
        
        print("=== 快速机器人控制 ===")
        print("WASD控制:")
        print("W - 前进")
        print("S - 后退")
        print("A - 左转")
        print("D - 右转")
        print("空格 - 停止")
        print("Q - 退出")
        print("========================")
        print("按键控制机器人...")

    def get_key(self):
        tty.setraw(sys.stdin.fileno())
        rlist, _, _ = select.select([sys.stdin], [], [], 0.1)
        if rlist:
            key = sys.stdin.read(1)
        else:
            key = ''
        termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.settings)
        return key

    def send_velocity(self, linear_x=0.0, angular_z=0.0):
        twist = Twist()
        twist.linear.x = linear_x
        twist.angular.z = angular_z
        self.pub.publish(twist)

    def run(self):
        try:
            while not rospy.is_shutdown():
                key = self.get_key().lower()
                
                if key == 'w':
                    self.send_velocity(self.linear_speed, 0.0)
                    print("\r前进", end='', flush=True)
                elif key == 's':
                    self.send_velocity(-self.linear_speed, 0.0)
                    print("\r后退", end='', flush=True)
                elif key == 'a':
                    self.send_velocity(0.0, self.angular_speed)
                    print("\r左转", end='', flush=True)
                elif key == 'd':
                    self.send_velocity(0.0, -self.angular_speed)
                    print("\r右转", end='', flush=True)
                elif key == ' ':
                    self.send_velocity(0.0, 0.0)
                    print("\r停止", end='', flush=True)
                elif key == 'q' or key == '\x03':  # q或Ctrl+C
                    print("\r退出控制")
                    break
                elif key == '':
                    # 没有按键时停止
                    self.send_velocity(0.0, 0.0)
                
        except KeyboardInterrupt:
            print("\n收到中断信号")
        except Exception as e:
            print(f"\n错误: {e}")
        finally:
            # 停止机器人
            self.send_velocity(0.0, 0.0)
            # 恢复终端设置
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.settings)
            print("控制结束")

if __name__ == '__main__':
    try:
        controller = FastRobotControl()
        controller.run()
    except rospy.ROSInterruptException:
        pass
