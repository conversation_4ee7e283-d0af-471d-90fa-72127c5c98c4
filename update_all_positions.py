#!/usr/bin/env python3
"""
一次性更新所有位置相关的文件
"""

import os
import shutil

def update_rviz_config():
    """更新RViz配置文件"""
    rviz_file = "/home/<USER>/maps/wmd.rviz"
    
    try:
        # 备份原文件
        if os.path.exists(rviz_file):
            shutil.copy2(rviz_file, rviz_file + ".backup")
            print(f"✅ 已备份RViz配置文件")
        
        # 读取文件内容
        with open(rviz_file, 'r') as f:
            content = f.read()
        
        # 替换焦点位置
        content = content.replace('X: -0.049', 'X: 0.065')
        content = content.replace('Y: 0.109', 'Y: 0.071')
        
        # 写回文件
        with open(rviz_file, 'w') as f:
            f.write(content)
        
        print("✅ RViz配置文件已更新")
        print("📍 新的焦点位置: (0.065, 0.071, 0.0)")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新RViz配置文件时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔄 开始更新所有位置配置...")
    print("📍 新位置: (0.065, 0.071, -0.286°)")
    print()
    
    # 更新RViz配置
    if update_rviz_config():
        print("✅ 所有配置文件已更新完成！")
        print()
        print("📋 更新摘要:")
        print("  - Python初始位置脚本: ✅")
        print("  - Shell初始位置脚本: ✅") 
        print("  - 通用位置设置脚本: ✅")
        print("  - RViz配置文件: ✅")
        print()
        print("🎯 现在所有脚本都使用统一的初始位置:")
        print("   位置: (0.065, 0.071, 0.000)")
        print("   朝向: -0.286度")
    else:
        print("❌ 更新过程中出现错误")

if __name__ == '__main__':
    main()
