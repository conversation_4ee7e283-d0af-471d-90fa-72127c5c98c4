#!/bin/bash
# 快速实时遥控脚本

echo "=== 快速实时遥控 ==="
echo "每次按键立即响应，短时间移动"

# 设置ROS环境
cd /home/<USER>/ucar_ws
source devel/setup.bash
export ROS_MASTER_URI=http://localhost:11311
export ROS_IP=**************
export ROS_HOSTNAME=**************

echo ""
echo "控制说明："
echo "W - 前进一小步"
echo "S - 后退一小步"
echo "A - 左转一小步"
echo "D - 右转一小步"
echo "Q - 退出"
echo ""

# 定义快速移动函数
quick_forward() {
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.2, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' > /dev/null 2>&1 &
    sleep 0.1
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' > /dev/null 2>&1 &
}

quick_backward() {
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: -0.2, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' > /dev/null 2>&1 &
    sleep 0.1
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' > /dev/null 2>&1 &
}

quick_left() {
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.3}}' > /dev/null 2>&1 &
    sleep 0.1
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' > /dev/null 2>&1 &
}

quick_right() {
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: -0.3}}' > /dev/null 2>&1 &
    sleep 0.1
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' > /dev/null 2>&1 &
}

stop_robot() {
    rostopic pub -1 /cmd_vel geometry_msgs/Twist '{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}' > /dev/null 2>&1
}

# 清理函数
cleanup() {
    echo ""
    echo "停止机器人..."
    stop_robot
    echo "控制结束"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

echo "开始快速控制（按键后立即响应）..."

# 主控制循环
while true; do
    read -n 1 -s key
    
    case ${key,,} in  # 转换为小写
        w)
            echo -n "↑"
            quick_forward
            ;;
        s)
            echo -n "↓"
            quick_backward
            ;;
        a)
            echo -n "←"
            quick_left
            ;;
        d)
            echo -n "→"
            quick_right
            ;;
        q)
            echo ""
            echo "退出控制..."
            break
            ;;
        *)
            echo -n "?"
            ;;
    esac
done

cleanup
